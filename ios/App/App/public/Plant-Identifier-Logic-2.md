"use client"

import type React from "react"

import { useState } from "react"
import {
  Camera,
  Leaf,
  User,
  Home,
  Plus,
  ArrowRight,
  Droplets,
  Sun,
  Thermometer,
  Loader2,
  Settings,
  ArrowLeft,
  Share,
  ImageIcon,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import Image from "next/image"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"

type Screen = "home" | "camera" | "results" | "collection" | "profile"

type PlantInCollection = {
  id: number
  name: string
  nickname: string
  health: number
  lastWateredDate: Date
  image: string
  status: "healthy" | "needs-attention"
  wateringFrequencyDays?: number
  notes?: string
}

export default function FloraVisionApp() {
  // Utility function to calculate days ago
  const getDaysAgo = (date: Date): string => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const plantDate = new Date(date)
    plantDate.setHours(0, 0, 0, 0)

    const diffTime = today.getTime() - plantDate.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) return "Today"
    if (diffDays === 1) return "Yesterday"
    return `${diffDays} days ago`
  }

  const [currentScreen, setCurrentScreen] = useState<Screen>("home")
  const [selectedPlant, setSelectedPlant] = useState<any>(null) // Can hold either identification or diagnosis result
  const [selectedTab, setSelectedTab] = useState("All")

  const [myPlants, setMyPlants] = useState<PlantInCollection[]>([
    {
      id: 1,
      name: "Peace Lily",
      nickname: "Sunny",
      health: 95,
      lastWateredDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      image: "/placeholder.svg?height=200&width=200",
      status: "healthy",
      wateringFrequencyDays: 7,
      notes: "Loves bright indirect light.",
    },
    {
      id: 2,
      name: "Rose",
      nickname: "Rose",
      health: 75,
      lastWateredDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      image: "/placeholder.svg?height=200&width=200",
      status: "needs-attention",
      wateringFrequencyDays: 3,
      notes: "Needs water.",
    },
    {
      id: 3,
      name: "Lily",
      nickname: "Lily",
      health: 88,
      lastWateredDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      image: "/placeholder.svg?height=200&width=200",
      status: "healthy",
      wateringFrequencyDays: 5,
      notes: "Beautiful flowers.",
    },
    {
      id: 4,
      name: "Daisy",
      nickname: "Daisy",
      health: 92,
      lastWateredDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      image: "/placeholder.svg?height=200&width=200",
      status: "healthy",
      wateringFrequencyDays: 4,
      notes: "Cheerful and bright.",
    },
    {
      id: 5,
      name: "Tulip",
      nickname: "Tulip",
      health: 70,
      lastWateredDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      image: "/placeholder.svg?height=200&width=200",
      status: "needs-attention",
      wateringFrequencyDays: 3,
      notes: "Needs more water.",
    },
    {
      id: 6,
      name: "Orchid",
      nickname: "Orchid",
      health: 95,
      lastWateredDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      image: "/placeholder.svg?height=200&width=200",
      status: "healthy",
      wateringFrequencyDays: 7,
      notes: "Exotic and elegant.",
    },
  ])
  const [nextPlantId, setNextPlantId] = useState(myPlants.length + 1)
  const [showAddPlantDialog, setShowAddPlantDialog] = useState(false)

  const recentScans = [
    {
      id: 1,
      name: "Fiddle Leaf Fig",
      date: "Identified on 2024-07-26",
      image: "/placeholder.svg?height=60&width=60",
    },
    {
      id: 2,
      name: "Succulent",
      date: "Identified on 2024-07-20",
      image: "/placeholder.svg?height=60&width=60",
    },
  ]

  const handleAddPlant = (nickname: string, wateringFrequencyDays: number | undefined, notes: string) => {
    if (selectedPlant) {
      const newPlant: PlantInCollection = {
        id: nextPlantId,
        name: selectedPlant.name,
        nickname: nickname,
        health: 100,
        lastWateredDate: new Date(),
        image: selectedPlant.image,
        status: "healthy",
        wateringFrequencyDays: wateringFrequencyDays,
        notes,
      }
      setMyPlants((prevPlants) => [...prevPlants, newPlant])
      setNextPlantId((prevId) => prevId + 1)
      setShowAddPlantDialog(false)
      setCurrentScreen("collection")
    }
  }

  const HomeScreen = () => (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between px-6 pt-14 pb-6">
        <h1 className="text-xl font-semibold">FloraVision</h1>
        <Button variant="ghost" size="icon" className="text-white hover:bg-gray-800 rounded-full">
          <User className="h-6 w-6" />
        </Button>
      </div>

      {/* Action Buttons */}
      <div className="px-6 mb-8">
        <div className="flex space-x-3">
          <Button
            onClick={() => setCurrentScreen("camera")}
            className="flex-1 bg-green-600 hover:bg-green-700 text-white rounded-full py-3 font-medium"
          >
            Identify Plant
          </Button>
          {/* Removed Diagnose Plant button */}
        </div>
      </div>

      {/* My Plants Section */}
      <div className="px-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">My Plants</h2>
        <div className="grid grid-cols-3 gap-3">
          {myPlants.slice(0, 3).map((plant) => (
            <div key={plant.id} className="bg-gray-800 rounded-2xl p-4 text-center">
              <div className="w-16 h-16 bg-gray-700 rounded-xl mx-auto mb-3 overflow-hidden">
                <Image
                  src={plant.image || "/placeholder.svg"}
                  alt={plant.nickname}
                  width={64}
                  height={64}
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="font-medium text-sm">{plant.nickname}</h3>
              <p className="text-xs text-gray-400 mt-1">Last watered {getDaysAgo(plant.lastWateredDate)}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Scans */}
      <div className="px-6 pb-24">
        <h2 className="text-xl font-semibold mb-4">Recent Scans</h2>
        <div className="space-y-3">
          {recentScans.map((scan) => (
            <div key={scan.id} className="flex items-center space-x-4 bg-gray-800 rounded-2xl p-4">
              <div className="w-12 h-12 bg-gray-700 rounded-xl overflow-hidden">
                <Image
                  src={scan.image || "/placeholder.svg"}
                  alt={scan.name}
                  width={48}
                  height={48}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex-1">
                <h3 className="font-medium">{scan.name}</h3>
                <p className="text-sm text-gray-400">{scan.date}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  // States for CameraScreen
  const [cameraMode, setCameraMode] = useState<"identify" | "diagnose">("identify")
  const [cameraPhotoFile, setCameraPhotoFile] = useState<string | null>(null)
  const [cameraRemarks, setCameraRemarks] = useState<string>("")
  const [isCameraProcessing, setIsCameraProcessing] = useState(false)

  const CameraScreen = () => {
    const handleTakePhoto = () => {
      setCameraPhotoFile("/placeholder.svg?height=300&width=300")
    }

    const handleUploadPhoto = () => {
      setCameraPhotoFile("/placeholder.svg?height=300&width=300")
    }

    const handleProcess = () => {
      setIsCameraProcessing(true)
      setCameraRemarks("") // Clear remarks after processing

      setTimeout(() => {
        if (cameraMode === "identify") {
          setSelectedPlant({
            type: "identification",
            name: "Monstera Deliciosa",
            scientificName: "Monstera deliciosa",
            confidence: 95,
            family: "Araceae",
            image: cameraPhotoFile || "/placeholder.svg?height=300&width=300",
          })
        } else {
          // Simulate diagnosis logic based on remarks and photo
          let issue = "No specific issue detected"
          let severity = "Low"
          let advice = "Your plant appears healthy based on provided information. Continue regular care."

          if (
            cameraRemarks.toLowerCase().includes("mushy roots") ||
            cameraRemarks.toLowerCase().includes("overwater")
          ) {
            issue = "Overwatering / Root Rot"
            severity = "High"
            advice =
              "Reduce watering frequency, ensure good drainage. Check roots for rot and prune affected parts. Repot if necessary with fresh, well-draining soil."
          } else if (
            cameraRemarks.toLowerCase().includes("yellow leaves") ||
            cameraRemarks.toLowerCase().includes("wilting")
          ) {
            issue = "Nutrient Deficiency / Underwatering"
            severity = "Medium"
            advice =
              "Check soil moisture deeply. If dry, water thoroughly. Consider fertilizing with a balanced liquid plant food."
          } else if (
            cameraRemarks.toLowerCase().includes("brown spots") ||
            cameraRemarks.toLowerCase().includes("pests")
          ) {
            issue = "Pest Infestation"
            severity = "High"
            advice =
              "Isolate the plant. Wipe leaves with neem oil solution. Increase humidity. Repeat treatment every few days."
          }

          setSelectedPlant({
            type: "diagnosis",
            issue,
            severity,
            advice,
            image: cameraPhotoFile,
            remarks: cameraRemarks,
          })
        }
        setIsCameraProcessing(false)
        setCameraPhotoFile(null) // Clear photo after processing
        setCurrentScreen("results")
      }, 2000)
    }

    return (
      <div className="min-h-screen bg-black relative">
        {/* Header */}
        <div className="absolute top-0 left-0 right-0 pt-14 px-6 z-10">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setCurrentScreen("home")
                setCameraPhotoFile(null)
                setCameraRemarks("")
              }}
              className="text-white hover:bg-white/20 rounded-full px-4"
            >
              Cancel
            </Button>
            {/* Removed top right buttons */}
          </div>
        </div>

        {/* Mode Tabs */}
        <div className="absolute top-28 left-1/2 transform -translate-x-1/2 z-10">
          <div className="flex bg-gray-800 rounded-full p-1">
            <Button
              onClick={() => setCameraMode("identify")}
              className={`px-6 py-2 rounded-full font-medium ${
                cameraMode === "identify" ? "bg-green-600 text-white" : "bg-transparent text-gray-400 hover:bg-gray-700"
              }`}
            >
              Identify
            </Button>
            <Button
              onClick={() => setCameraMode("diagnose")}
              className={`px-6 py-2 rounded-full font-medium ${
                cameraMode === "diagnose" ? "bg-green-600 text-white" : "bg-transparent text-gray-400 hover:bg-gray-700"
              }`}
            >
              Diagnose
            </Button>
          </div>
        </div>

        {/* Image Preview / Camera Viewfinder */}
        <div className="absolute inset-0 bg-gray-900 flex items-center justify-center">
          {cameraPhotoFile ? (
            <Image
              src={cameraPhotoFile || "/placeholder.svg"}
              alt="Plant Preview"
              width={400}
              height={600}
              className="w-full h-full object-cover"
            />
          ) : (
            <Camera className="h-24 w-24 text-gray-700" />
          )}
        </div>

        {/* Remarks Textarea (conditional) */}
        {cameraMode === "diagnose" && (
          <div className="absolute bottom-40 left-0 right-0 px-6 z-10">
            <Textarea
              value={cameraRemarks}
              onChange={(e) => setCameraRemarks(e.target.value)}
              placeholder="Enter remarks about your plant's symptoms (e.g., 'Mushy roots', 'Yellowing leaves')"
              className="bg-gray-800 border-gray-700 text-white rounded-2xl min-h-[80px] resize-none"
            />
          </div>
        )}

        {/* Capture/Process Button & Gallery Button */}
        <div className="absolute bottom-8 left-0 right-0 flex justify-center items-center space-x-4 z-10">
          <Button
            onClick={handleUploadPhoto}
            className="w-16 h-16 bg-gray-800 hover:bg-gray-700 rounded-full flex items-center justify-center text-white"
          >
            <ImageIcon className="h-7 w-7" />
          </Button>
          <Button
            onClick={handleProcess}
            disabled={!cameraPhotoFile || isCameraProcessing}
            className="w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-lg transition-all duration-200 active:scale-95 disabled:opacity-50"
          >
            {isCameraProcessing ? (
              <Loader2 className="h-8 w-8 animate-spin text-gray-700" />
            ) : (
              <div className="w-16 h-16 rounded-full bg-white shadow-inner" />
            )}
          </Button>
          <Button
            onClick={handleTakePhoto}
            className="w-16 h-16 bg-gray-800 hover:bg-gray-700 rounded-full flex items-center justify-center text-white"
          >
            <Camera className="h-7 w-7" />
          </Button>
        </div>
      </div>
    )
  }

  const ResultsScreen = () => {
    if (!selectedPlant) {
      return (
        <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
          <p>No plant data to display.</p>
        </div>
      )
    }

    if (selectedPlant.type === "diagnosis") {
      return (
        <div className="min-h-screen bg-gray-900 text-white">
          {/* Header */}
          <div className="flex items-center justify-between px-6 pt-14 pb-6">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setCurrentScreen("home")}
              className="text-white hover:bg-gray-800 rounded-full"
            >
              <ArrowLeft className="h-6 w-6" />
            </Button>
            <h1 className="text-xl font-semibold">Diagnosis Result</h1>
            <Button variant="ghost" size="icon" className="text-white hover:bg-gray-800 rounded-full">
              <Share className="h-6 w-6" />
            </Button>
          </div>

          {/* Plant Image */}
          {selectedPlant.image && (
            <div className="px-6 mb-6">
              <div className="bg-gray-100 rounded-3xl h-64 overflow-hidden">
                <Image
                  src={selectedPlant.image || "/placeholder.svg"}
                  alt="Diagnosed Plant"
                  width={400}
                  height={256}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          )}

          {/* Diagnosis Info */}
          <div className="px-6 mb-8">
            <div className="bg-gray-800 rounded-3xl p-6">
              <h2 className="text-2xl font-bold mb-2">{selectedPlant.issue}</h2>
              <p className="text-gray-400 mb-4">Severity: {selectedPlant.severity}</p>
              <p className="text-gray-300">{selectedPlant.advice}</p>
              {selectedPlant.remarks && <p className="text-gray-500 text-sm mt-4">Remarks: {selectedPlant.remarks}</p>}
            </div>
          </div>

          {/* Action Buttons (optional for diagnosis) */}
          <div className="px-6 pb-24">
            <Button
              onClick={() => setCurrentScreen("home")}
              className="w-full bg-green-600 hover:bg-green-700 text-white rounded-full py-3 font-medium"
            >
              Back to Home
            </Button>
          </div>
        </div>
      )
    }

    // Default to identification results
    return (
      <div className="min-h-screen bg-gray-900 text-white">
        {/* Header */}
        <div className="flex items-center justify-between px-6 pt-14 pb-6">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setCurrentScreen("home")}
            className="text-white hover:bg-gray-800 rounded-full"
          >
            <ArrowLeft className="h-6 w-6" />
          </Button>
          <Button variant="ghost" size="icon" className="text-white hover:bg-gray-800 rounded-full">
            <Share className="h-6 w-6" />
          </Button>
        </div>

        {/* Plant Image */}
        <div className="px-6 mb-6">
          <div className="bg-gray-100 rounded-3xl h-64 overflow-hidden">
            <Image
              src={selectedPlant?.image || "/placeholder.svg?height=300&width=400&query=monstera+deliciosa"}
              alt={selectedPlant?.name || "Plant"}
              width={400}
              height={256}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        {/* Plant Info */}
        <div className="px-6 mb-8">
          <div className="bg-gray-800 rounded-3xl p-6">
            <h2 className="text-2xl font-bold mb-2">{selectedPlant?.name || "Monstera Deliciosa"}</h2>
            <p className="text-gray-400 mb-4">
              Scientific Name: {selectedPlant?.scientificName || "Monstera deliciosa"}
            </p>
            <p className="text-green-400 font-medium">Confidence: {selectedPlant?.confidence || 95}%</p>
          </div>
        </div>

        {/* Care Requirements */}
        <div className="px-6 mb-8">
          <h3 className="text-xl font-semibold mb-4">Care Requirements</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-4 bg-gray-800 rounded-2xl p-4">
              <div className="w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center">
                <Sun className="h-5 w-5 text-white" />
              </div>
              <div>
                <h4 className="font-medium">Light</h4>
                <p className="text-sm text-gray-400">Bright, indirect sunlight</p>
              </div>
            </div>
            <div className="flex items-center space-x-4 bg-gray-800 rounded-2xl p-4">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <Droplets className="h-5 w-5 text-white" />
              </div>
              <div>
                <h4 className="font-medium">Water</h4>
                <p className="text-sm text-gray-400">Water when the top inch of soil is dry</p>
              </div>
            </div>
            <div className="flex items-center space-x-4 bg-gray-800 rounded-2xl p-4">
              <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                <Thermometer className="h-5 w-5 text-white" />
              </div>
              <div>
                <h4 className="font-medium">Temperature</h4>
                <p className="text-sm text-gray-400">65-85°F (18-29°C)</p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="px-6 pb-24">
          <div className="flex space-x-3">
            <Button
              onClick={() => setShowAddPlantDialog(true)}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white rounded-full py-3 font-medium"
            >
              Add to My Plants
            </Button>
            <Button
              variant="outline"
              className="flex-1 border-gray-600 text-white hover:bg-gray-800 rounded-full py-3 font-medium bg-transparent"
            >
              Share Result
            </Button>
          </div>
        </div>

        {showAddPlantDialog && (
          <AddPlantDialog
            selectedPlant={selectedPlant}
            onClose={() => setShowAddPlantDialog(false)}
            onSave={handleAddPlant}
          />
        )}
      </div>
    )
  }

  const CollectionScreen = () => (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between px-6 pt-14 pb-6">
        <h1 className="text-xl font-semibold">My Plants</h1>
        <Button variant="ghost" size="icon" className="text-white hover:bg-gray-800 rounded-full">
          <Plus className="h-6 w-6" />
        </Button>
      </div>

      {/* Filter Tabs */}
      <div className="px-6 mb-6">
        <div className="flex space-x-6">
          {["All", "Indoor", "Outdoor", "Flowering"].map((tab) => (
            <button
              key={tab}
              onClick={() => setSelectedTab(tab)}
              className={`pb-2 font-medium ${
                selectedTab === tab ? "text-white border-b-2 border-white" : "text-gray-400 hover:text-gray-300"
              }`}
            >
              {tab}
            </button>
          ))}
        </div>
      </div>

      {/* Plants Grid */}
      <div className="px-6 pb-24">
        <div className="grid grid-cols-2 gap-4">
          {myPlants.map((plant) => (
            <div key={plant.id} className="bg-gray-100 rounded-3xl p-4 text-center">
              <div className="h-32 bg-gray-200 rounded-2xl mb-4 overflow-hidden">
                <Image
                  src={plant.image || "/placeholder.svg"}
                  alt={plant.nickname}
                  width={150}
                  height={128}
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="font-semibold text-gray-900">{plant.nickname}</h3>
              <p className={`text-sm mt-1 ${plant.status === "healthy" ? "text-green-600" : "text-orange-600"}`}>
                {plant.status === "healthy" ? "Healthy" : "Needs Water"}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const ProfileScreen = () => (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="flex items-center justify-between px-6 pt-14 pb-8">
        <h1 className="text-xl font-semibold">Profile</h1>
        <Button variant="ghost" size="icon" className="text-white hover:bg-gray-800 rounded-full">
          <Settings className="h-6 w-6" />
        </Button>
      </div>

      {/* Profile Info */}
      <div className="px-6 mb-8 text-center">
        <div className="w-32 h-32 bg-orange-200 rounded-full mx-auto mb-4 overflow-hidden">
          <Image
            src="/placeholder.svg?height=128&width=128"
            alt="Profile"
            width={128}
            height={128}
            className="w-full h-full object-cover"
          />
        </div>
        <h2 className="text-2xl font-bold">Olivia Green</h2>
        <p className="text-gray-400 mt-1">Plant Enthusiast</p>
        <Button className="bg-gray-700 hover:bg-gray-600 text-white rounded-full px-8 py-2 mt-6">Edit Profile</Button>
      </div>

      {/* Settings */}
      <div className="px-6 mb-8">
        <h3 className="text-xl font-semibold mb-4">Settings</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-lg">Notifications</span>
            <Switch defaultChecked />
          </div>
          <div className="flex items-center justify-between py-4">
            <span className="text-lg">Terms of Service</span>
            <ArrowRight className="h-5 w-5 text-gray-400" />
          </div>
        </div>
      </div>

      {/* Log Out */}
      <div className="px-6 pb-24">
        <Button className="w-full bg-gray-700 hover:bg-gray-600 text-white rounded-full py-3 font-medium">
          Log Out
        </Button>
      </div>
    </div>
  )

  interface AddPlantDialogProps {
    selectedPlant: any
    onClose: () => void
    onSave: (nickname: string, wateringFrequencyDays: number | undefined, notes: string) => void
  }

  const AddPlantDialog: React.FC<AddPlantDialogProps> = ({ selectedPlant, onClose, onSave }) => {
    const [nickname, setNickname] = useState(selectedPlant?.name || "")
    const [wateringFrequencyInput, setWateringFrequencyInput] = useState<string>("")
    const [notes, setNotes] = useState("")

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      const wateringFrequencyDays = wateringFrequencyInput ? Number.parseInt(wateringFrequencyInput, 10) : undefined
      onSave(nickname, wateringFrequencyDays, notes)
    }

    return (
      <Dialog open={true} onOpenChange={onClose}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white rounded-3xl">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold">Add {selectedPlant?.name}</DialogTitle>
            <DialogDescription className="text-gray-400">Customize care details for your new plant.</DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="space-y-4 py-4">
              <div>
                <Label htmlFor="nickname" className="text-sm font-medium text-gray-300">
                  Plant Nickname
                </Label>
                <Input
                  id="nickname"
                  value={nickname}
                  onChange={(e) => setNickname(e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white rounded-xl mt-2"
                />
              </div>
              <div>
                <Label htmlFor="watering" className="text-sm font-medium text-gray-300">
                  Watering Schedule (Days)
                </Label>
                <Input
                  id="watering"
                  type="number"
                  placeholder="e.g., 7"
                  value={wateringFrequencyInput}
                  onChange={(e) => setWateringFrequencyInput(e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white rounded-xl mt-2"
                />
              </div>
              <div>
                <Label htmlFor="notes" className="text-sm font-medium text-gray-300">
                  Care Notes
                </Label>
                <Textarea
                  id="notes"
                  placeholder="Any specific observations or care tips..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white rounded-xl mt-2 min-h-[80px] resize-none"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type="submit"
                className="w-full bg-green-600 hover:bg-green-700 text-white rounded-full py-3 font-medium"
              >
                Save Plant
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    )
  }

  const renderScreen = () => {
    switch (currentScreen) {
      case "home":
        return <HomeScreen />
      case "camera":
        return <CameraScreen />
      case "results":
        return <ResultsScreen />
      case "collection":
        return <CollectionScreen />
      case "profile":
        return <ProfileScreen />
      default:
        return <HomeScreen />
    }
  }

  return (
    <div className="max-w-sm mx-auto bg-gray-900 min-h-screen relative">
      {renderScreen()}

      {/* Bottom Navigation */}
      {currentScreen !== "camera" && currentScreen !== "results" && (
        <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-sm bg-gray-900 border-t border-gray-800">
          <div className="flex items-center justify-around py-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentScreen("home")}
              className={`flex flex-col items-center py-2 px-4 rounded-lg ${
                currentScreen === "home" ? "text-white" : "text-gray-500"
              }`}
            >
              <Home className="h-6 w-6 mb-1" />
              <span className="text-xs">Home</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setCurrentScreen("camera")
                setCameraMode("identify") // Ensure it defaults to identify when navigating from nav
              }}
              className={`flex flex-col items-center py-2 px-4 rounded-lg ${
                currentScreen === "camera" && cameraMode === "identify" ? "text-white" : "text-gray-500"
              }`}
            >
              <Camera className="h-6 w-6 mb-1" />
              <span className="text-xs">Identify Plants</span>
            </Button>
            {/* Removed Diagnose button from navigation */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentScreen("collection")}
              className={`flex flex-col items-center py-2 px-4 rounded-lg ${
                currentScreen === "collection" ? "text-white" : "text-gray-500"
              }`}
            >
              <Leaf className="h-6 w-6 mb-1" />
              <span className="text-xs">My Plants</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentScreen("profile")}
              className={`flex flex-col items-center py-2 px-4 rounded-lg ${
                currentScreen === "profile" ? "text-white" : "text-gray-500"
              }`}
            >
              <User className="h-6 w-6 mb-1" />
              <span className="text-xs">Profile</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
