// Client-side OpenRouter API integration for plant identification
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';

export interface PlantIdentificationResult {
  name: string;
  scientificName: string;
  confidence: number;
  family?: string;
  description?: string;
  careInstructions?: {
    light: string;
    water: string;
    temperature: string;
    humidity?: string;
    soil?: string;
  };
}

export interface PlantDiagnosisResult {
  issue: string;
  severity: 'Low' | 'Medium' | 'High';
  advice: string;
  confidence: number;
}

export class OpenRouterError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = 'OpenRouterError';
  }
}

// Convert image file to base64 data URL
export async function imageToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert image to base64'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read image file'));
    reader.readAsDataURL(file);
  });
}

// Make API call to OpenRouter via Next.js API route
async function callOpenRouterAPI(endpoint: string, data: any): Promise<any> {
  try {
    const response = await fetch(`/api/openrouter/${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new OpenRouterError(
        `API error: ${response.status} ${response.statusText} - ${errorText}`,
        response.status
      );
    }

    const result = await response.json();
    return result;
  } catch (error) {
    if (error instanceof OpenRouterError) {
      throw error;
    }
    throw new OpenRouterError(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Identify a plant from an image
export async function identifyPlant(imageDataUrl: string): Promise<PlantIdentificationResult> {
  try {
    const result = await callOpenRouterAPI('identify', { imageDataUrl });
    return result;
  } catch (error) {
    if (error instanceof OpenRouterError) {
      throw error;
    }
    throw new OpenRouterError(`Failed to identify plant: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Diagnose plant health issues
export async function diagnosePlant(imageDataUrl: string, symptoms: string): Promise<PlantDiagnosisResult> {
  try {
    const result = await callOpenRouterAPI('diagnose', { imageDataUrl, symptoms });
    return result;
  } catch (error) {
    if (error instanceof OpenRouterError) {
      throw error;
    }
    throw new OpenRouterError(`Failed to diagnose plant: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Handle camera capture using Capacitor Camera plugin
export async function captureImage(): Promise<File | null> {
  try {
    const image = await Camera.getPhoto({
      quality: 90,
      allowEditing: false,
      resultType: CameraResultType.DataUrl,
      source: CameraSource.Camera,
    });

    if (image.dataUrl) {
      // Convert data URL to File object
      const response = await fetch(image.dataUrl);
      const blob = await response.blob();
      const file = new File([blob], 'camera-image.jpg', { type: 'image/jpeg' });
      return file;
    }
    return null;
  } catch (error) {
    console.error('Error capturing image with Capacitor Camera:', error);
    // Fallback to HTML input for web
    return captureImageFallback();
  }
}

// Fallback camera capture for web browsers
async function captureImageFallback(): Promise<File | null> {
  return new Promise((resolve) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.capture = 'environment'; // Use rear camera on mobile

    input.onchange = (event) => {
      const target = event.target as HTMLInputElement;
      const file = target.files?.[0];
      resolve(file || null);
    };

    input.oncancel = () => resolve(null);
    input.click();
  });
}

// Handle image upload from gallery using Capacitor Camera plugin
export async function uploadImage(): Promise<File | null> {
  try {
    const image = await Camera.getPhoto({
      quality: 90,
      allowEditing: false,
      resultType: CameraResultType.DataUrl,
      source: CameraSource.Photos,
    });

    if (image.dataUrl) {
      // Convert data URL to File object
      const response = await fetch(image.dataUrl);
      const blob = await response.blob();
      const file = new File([blob], 'gallery-image.jpg', { type: 'image/jpeg' });
      return file;
    }
    return null;
  } catch (error) {
    console.error('Error selecting image from gallery:', error);
    // Fallback to HTML input for web
    return uploadImageFallback();
  }
}

// Fallback image upload for web browsers
async function uploadImageFallback(): Promise<File | null> {
  return new Promise((resolve) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';

    input.onchange = (event) => {
      const target = event.target as HTMLInputElement;
      const file = target.files?.[0];
      resolve(file || null);
    };

    input.oncancel = () => resolve(null);
    input.click();
  });
}
