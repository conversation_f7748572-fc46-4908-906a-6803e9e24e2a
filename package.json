{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "build:mobile": "next build && npx cap sync", "ios": "npm run build:mobile && npx cap open ios", "android": "npm run build:mobile && npx cap open android", "build:ios": "npm run build:mobile && npx cap build ios", "build:android": "npm run build:mobile && npx cap build android"}, "dependencies": {"@capacitor/android": "^7.4.2", "@capacitor/camera": "^7.0.1", "@capacitor/cli": "^6.2.1", "@capacitor/core": "^7.4.2", "@capacitor/ios": "^7.4.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.526.0", "next": "15.4.4", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}