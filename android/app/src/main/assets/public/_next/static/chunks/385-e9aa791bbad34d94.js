"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[385],{381:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},901:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(8229)._(r(2115)).default.createContext(null)},968:(e,t,r)=>{r.d(t,{b:()=>l});var n=r(2115),o=r(3655),a=r(5155),i=n.forwardRef((e,t)=>(0,a.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},1007:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1154:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1193:(e,t)=>{function r(e){var t;let{config:r,src:n,width:o,quality:a}=e,i=a||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+o+"&q="+i+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},1469:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return s},getImageProps:function(){return l}});let n=r(8229),o=r(8883),a=r(3063),i=n._(r(1193));function l(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let s=a.Image},2085:(e,t,r)=>{r.d(t,{F:()=>i});var n=r(2596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,s=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let a=o(t)||o(n);return i[e][a]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,s,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2098:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2138:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2464:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return n}});let n=r(8229)._(r(2115)).default.createContext({})},2596:(e,t,r)=>{r.d(t,{$:()=>n});function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n}},2712:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(2115),o=globalThis?.document?n.useLayoutEffect:()=>{}},3063:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return w}});let n=r(8229),o=r(6966),a=r(5155),i=o._(r(2115)),l=n._(r(7650)),s=n._(r(5564)),c=r(8883),d=r(5840),u=r(6752);r(3230);let f=r(901),p=n._(r(1193)),m=r(6654),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0};function g(e,t,r,n,o,a,i){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&o(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,o=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>o,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{o=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function v(e){return i.use?{fetchPriority:e}:{fetchpriority:e}}let b=(0,i.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:o,height:l,width:s,decoding:c,className:d,style:u,fetchPriority:f,placeholder:p,loading:h,unoptimized:b,fill:y,onLoadRef:w,onLoadingCompleteRef:x,setBlurComplete:k,setShowAltText:E,sizesInput:C,onLoad:P,onError:j,...O}=e,S=(0,i.useCallback)(e=>{e&&(j&&(e.src=e.src),e.complete&&g(e,p,w,x,k,b,C))},[r,p,w,x,k,j,b,C]),A=(0,m.useMergedRef)(t,S);return(0,a.jsx)("img",{...O,...v(f),loading:h,width:s,height:l,decoding:c,"data-nimg":y?"fill":"1",className:d,style:u,sizes:o,srcSet:n,src:r,ref:A,onLoad:e=>{g(e.currentTarget,p,w,x,k,b,C)},onError:e=>{E(!0),"empty"!==p&&k(!0),j&&j(e)}})});function y(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...v(r.fetchPriority)};return t&&l.default.preload?(l.default.preload(r.src,n),null):(0,a.jsx)(s.default,{children:(0,a.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let w=(0,i.forwardRef)((e,t)=>{let r=(0,i.useContext)(f.RouterContext),n=(0,i.useContext)(u.ImageConfigContext),o=(0,i.useMemo)(()=>{var e;let t=h||n||d.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),o=t.deviceSizes.sort((e,t)=>e-t),a=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:o,qualities:a}},[n]),{onLoad:l,onLoadingComplete:s}=e,m=(0,i.useRef)(l);(0,i.useEffect)(()=>{m.current=l},[l]);let g=(0,i.useRef)(s);(0,i.useEffect)(()=>{g.current=s},[s]);let[v,w]=(0,i.useState)(!1),[x,k]=(0,i.useState)(!1),{props:E,meta:C}=(0,c.getImgProps)(e,{defaultLoader:p.default,imgConf:o,blurComplete:v,showAltText:x});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b,{...E,unoptimized:C.unoptimized,placeholder:C.placeholder,fill:C.fill,onLoadRef:m,onLoadingCompleteRef:g,setBlurComplete:w,setShowAltText:k,sizesInput:e.sizes,ref:t}),C.priority?(0,a.jsx)(y,{isAppRouter:!r,imgAttributes:E}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3384:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},3655:(e,t,r)=>{r.d(t,{hO:()=>s,sG:()=>l});var n=r(2115),o=r(7650),a=r(9708),i=r(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?r:t,{...a,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},4036:(e,t,r)=>{r.d(t,{bm:()=>tl,UC:()=>to,VY:()=>ti,hJ:()=>tn,ZL:()=>tr,bL:()=>tt,hE:()=>ta});var n,o,a,i=r(2115),l=r.t(i,2),s=r(5185),c=r(6101),d=r(6081),u=r(2712),f=l[" useId ".trim().toString()]||(()=>void 0),p=0;function m(e){let[t,r]=i.useState(f());return(0,u.N)(()=>{e||r(e=>e??String(p++))},[e]),e||(t?`radix-${t}`:"")}var h=r(5845),g=r(3655);function v(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}var b=r(5155),y="dismissableLayer.update",w=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),x=i.forwardRef((e,t)=>{var r,n;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:l,onPointerDownOutside:d,onFocusOutside:u,onInteractOutside:f,onDismiss:p,...m}=e,h=i.useContext(w),[x,C]=i.useState(null),P=null!=(n=null==x?void 0:x.ownerDocument)?n:null==(r=globalThis)?void 0:r.document,[,j]=i.useState({}),O=(0,c.s)(t,e=>C(e)),S=Array.from(h.layers),[A]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),R=S.indexOf(A),N=x?S.indexOf(x):-1,L=h.layersWithOutsidePointerEventsDisabled.size>0,M=N>=R,_=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=v(e),o=i.useRef(!1),a=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){E("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",a.current),a.current=t,r.addEventListener("click",a.current,{once:!0})):t()}else r.removeEventListener("click",a.current);o.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",a.current)}},[r,n]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,r=[...h.branches].some(e=>e.contains(t));M&&!r&&(null==d||d(e),null==f||f(e),e.defaultPrevented||null==p||p())},P),z=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=v(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&E("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...h.branches].some(e=>e.contains(t))&&(null==u||u(e),null==f||f(e),e.defaultPrevented||null==p||p())},P);return!function(e,t=globalThis?.document){let r=v(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{N===h.layers.size-1&&(null==l||l(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},P),i.useEffect(()=>{if(x)return a&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(o=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(x)),h.layers.add(x),k(),()=>{a&&1===h.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=o)}},[x,P,a,h]),i.useEffect(()=>()=>{x&&(h.layers.delete(x),h.layersWithOutsidePointerEventsDisabled.delete(x),k())},[x,h]),i.useEffect(()=>{let e=()=>j({});return document.addEventListener(y,e),()=>document.removeEventListener(y,e)},[]),(0,b.jsx)(g.sG.div,{...m,ref:O,style:{pointerEvents:L?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,s.m)(e.onFocusCapture,z.onFocusCapture),onBlurCapture:(0,s.m)(e.onBlurCapture,z.onBlurCapture),onPointerDownCapture:(0,s.m)(e.onPointerDownCapture,_.onPointerDownCapture)})});function k(){let e=new CustomEvent(y);document.dispatchEvent(e)}function E(e,t,r,n){let{discrete:o}=n,a=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&a.addEventListener(e,t,{once:!0}),o?(0,g.hO)(a,i):a.dispatchEvent(i)}x.displayName="DismissableLayer",i.forwardRef((e,t)=>{let r=i.useContext(w),n=i.useRef(null),o=(0,c.s)(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,b.jsx)(g.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var C="focusScope.autoFocusOnMount",P="focusScope.autoFocusOnUnmount",j={bubbles:!1,cancelable:!0},O=i.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...l}=e,[s,d]=i.useState(null),u=v(o),f=v(a),p=i.useRef(null),m=(0,c.s)(t,e=>d(e)),h=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(n){let e=function(e){if(h.paused||!s)return;let t=e.target;s.contains(t)?p.current=t:R(p.current,{select:!0})},t=function(e){if(h.paused||!s)return;let t=e.relatedTarget;null!==t&&(s.contains(t)||R(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&R(s)});return s&&r.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,s,h.paused]),i.useEffect(()=>{if(s){N.add(h);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(C,j);s.addEventListener(C,u),s.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(R(n,{select:t}),document.activeElement!==r)return}(S(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&R(s))}return()=>{s.removeEventListener(C,u),setTimeout(()=>{let t=new CustomEvent(P,j);s.addEventListener(P,f),s.dispatchEvent(t),t.defaultPrevented||R(null!=e?e:document.body,{select:!0}),s.removeEventListener(P,f),N.remove(h)},0)}}},[s,u,f,h]);let y=i.useCallback(e=>{if(!r&&!n||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,a]=function(e){let t=S(e);return[A(t,e),A(t.reverse(),e)]}(t);n&&a?e.shiftKey||o!==a?e.shiftKey&&o===n&&(e.preventDefault(),r&&R(a,{select:!0})):(e.preventDefault(),r&&R(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,h.paused]);return(0,b.jsx)(g.sG.div,{tabIndex:-1,...l,ref:m,onKeyDown:y})});function S(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function A(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function R(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}O.displayName="FocusScope";var N=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=L(e,t)).unshift(t)},remove(t){var r;null==(r=(e=L(e,t))[0])||r.resume()}}}();function L(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var M=r(7650),_=i.forwardRef((e,t)=>{var r,n;let{container:o,...a}=e,[l,s]=i.useState(!1);(0,u.N)(()=>s(!0),[]);let c=o||l&&(null==(n=globalThis)||null==(r=n.document)?void 0:r.body);return c?M.createPortal((0,b.jsx)(g.sG.div,{...a,ref:t}),c):null});_.displayName="Portal";var z=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,o]=i.useState(),a=i.useRef(null),l=i.useRef(e),s=i.useRef("none"),[c,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return i.useEffect(()=>{let e=I(a.current);s.current="mounted"===c?e:"none"},[c]),(0,u.N)(()=>{let t=a.current,r=l.current;if(r!==e){let n=s.current,o=I(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):r&&n!==o?d("ANIMATION_OUT"):d("UNMOUNT"),l.current=e}},[e,d]),(0,u.N)(()=>{if(n){var e;let t,r=null!=(e=n.ownerDocument.defaultView)?e:window,o=e=>{let o=I(a.current).includes(e.animationName);if(e.target===n&&o&&(d("ANIMATION_END"),!l.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},i=e=>{e.target===n&&(s.current=I(a.current))};return n.addEventListener("animationstart",i),n.addEventListener("animationcancel",o),n.addEventListener("animationend",o),()=>{r.clearTimeout(t),n.removeEventListener("animationstart",i),n.removeEventListener("animationcancel",o),n.removeEventListener("animationend",o)}}d("ANIMATION_END")},[n,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:i.useCallback(e=>{a.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof r?r({present:n.isPresent}):i.Children.only(r),a=(0,c.s)(n.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof r||n.isPresent?i.cloneElement(o,{ref:a}):null};function I(e){return(null==e?void 0:e.animationName)||"none"}z.displayName="Presence";var D=0;function T(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var U=function(){return(U=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function F(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;Object.create;var $=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),W="width-before-scroll-bar";function G(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var B="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,q=new WeakMap;function H(e){return e}var V=function(e){void 0===e&&(e={});var t,r,n,o=(void 0===t&&(t=H),r=[],n=!1,{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var o=t(e,n);return r.push(o),function(){r=r.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(n=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){n=!0;var t=[];if(r.length){var o=r;r=[],o.forEach(e),t=r}var a=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return o.options=U({async:!0,ssr:!1},e),o}(),X=function(){},K=i.forwardRef(function(e,t){var r,n,o,a,l=i.useRef(null),s=i.useState({onScrollCapture:X,onWheelCapture:X,onTouchMoveCapture:X}),c=s[0],d=s[1],u=e.forwardProps,f=e.children,p=e.className,m=e.removeScrollBar,h=e.enabled,g=e.shards,v=e.sideCar,b=e.noRelative,y=e.noIsolation,w=e.inert,x=e.allowPinchZoom,k=e.as,E=e.gapMode,C=F(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(r=[l,t],n=function(e){return r.forEach(function(t){return G(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,a=o.facade,B(function(){var e=q.get(a);if(e){var t=new Set(e),n=new Set(r),o=a.current;t.forEach(function(e){n.has(e)||G(e,null)}),n.forEach(function(e){t.has(e)||G(e,o)})}q.set(a,r)},[r]),a),j=U(U({},C),c);return i.createElement(i.Fragment,null,h&&i.createElement(v,{sideCar:V,removeScrollBar:m,shards:g,noRelative:b,noIsolation:y,inert:w,setCallbacks:d,allowPinchZoom:!!x,lockRef:l,gapMode:E}),u?i.cloneElement(i.Children.only(f),U(U({},j),{ref:P})):i.createElement(void 0===k?"div":k,U({},j,{className:p,ref:P}),f))});K.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},K.classNames={fullWidth:W,zeroRight:$};var Y=function(e){var t=e.sideCar,r=F(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return i.createElement(n,U({},r))};Y.isSideCarExport=!0;var Z=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=a||r.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=n:o.appendChild(document.createTextNode(n)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},J=function(){var e=Z();return function(t,r){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},Q=function(){var e=J();return function(t){return e(t.styles,t.dynamic),null}},ee={left:0,top:0,right:0,gap:0},et=function(e){return parseInt(e||"",10)||0},er=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[et(r),et(n),et(o)]},en=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return ee;var t=er(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},eo=Q(),ea="data-scroll-locked",ei=function(e,t,r,n){var o=e.left,a=e.top,i=e.right,l=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(l,"px ").concat(n,";\n  }\n  body[").concat(ea,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(l,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat($," {\n    right: ").concat(l,"px ").concat(n,";\n  }\n  \n  .").concat(W," {\n    margin-right: ").concat(l,"px ").concat(n,";\n  }\n  \n  .").concat($," .").concat($," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(W," .").concat(W," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(ea,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},el=function(){var e=parseInt(document.body.getAttribute(ea)||"0",10);return isFinite(e)?e:0},es=function(){i.useEffect(function(){return document.body.setAttribute(ea,(el()+1).toString()),function(){var e=el()-1;e<=0?document.body.removeAttribute(ea):document.body.setAttribute(ea,e.toString())}},[])},ec=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;es();var a=i.useMemo(function(){return en(o)},[o]);return i.createElement(eo,{styles:ei(a,!t,o,r?"":"!important")})},ed=!1;if("undefined"!=typeof window)try{var eu=Object.defineProperty({},"passive",{get:function(){return ed=!0,!0}});window.addEventListener("test",eu,eu),window.removeEventListener("test",eu,eu)}catch(e){ed=!1}var ef=!!ed&&{passive:!1},ep=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},em=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),eh(e,n)){var o=eg(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},eh=function(e,t){return"v"===e?ep(t,"overflowY"):ep(t,"overflowX")},eg=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ev=function(e,t,r,n,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),l=i*n,s=r.target,c=t.contains(s),d=!1,u=l>0,f=0,p=0;do{if(!s)break;var m=eg(e,s),h=m[0],g=m[1]-m[2]-i*h;(h||g)&&eh(e,s)&&(f+=g,p+=h);var v=s.parentNode;s=v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE?v.host:v}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return u&&(o&&1>Math.abs(f)||!o&&l>f)?d=!0:!u&&(o&&1>Math.abs(p)||!o&&-l>p)&&(d=!0),d},eb=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ey=function(e){return[e.deltaX,e.deltaY]},ew=function(e){return e&&"current"in e?e.current:e},ex=0,ek=[];let eE=(n=function(e){var t=i.useRef([]),r=i.useRef([0,0]),n=i.useRef(),o=i.useState(ex++)[0],a=i.useState(Q)[0],l=i.useRef(e);i.useEffect(function(){l.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(ew),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,a=eb(e),i=r.current,s="deltaX"in e?e.deltaX:i[0]-a[0],c="deltaY"in e?e.deltaY:i[1]-a[1],d=e.target,u=Math.abs(s)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===u&&"range"===d.type)return!1;var f=em(u,d);if(!f)return!0;if(f?o=u:(o="v"===u?"h":"v",f=em(u,d)),!f)return!1;if(!n.current&&"changedTouches"in e&&(s||c)&&(n.current=o),!o)return!0;var p=n.current||o;return ev(p,t,e,"h"===p?s:c,!0)},[]),c=i.useCallback(function(e){if(ek.length&&ek[ek.length-1]===a){var r="deltaY"in e?ey(e):eb(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(l.current.shards||[]).map(ew).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=i.useCallback(function(e,r,n,o){var a={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),u=i.useCallback(function(e){r.current=eb(e),n.current=void 0},[]),f=i.useCallback(function(t){d(t.type,ey(t),t.target,s(t,e.lockRef.current))},[]),p=i.useCallback(function(t){d(t.type,eb(t),t.target,s(t,e.lockRef.current))},[]);i.useEffect(function(){return ek.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,ef),document.addEventListener("touchmove",c,ef),document.addEventListener("touchstart",u,ef),function(){ek=ek.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,ef),document.removeEventListener("touchmove",c,ef),document.removeEventListener("touchstart",u,ef)}},[]);var m=e.removeScrollBar,h=e.inert;return i.createElement(i.Fragment,null,h?i.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?i.createElement(ec,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},V.useMedium(n),Y);var eC=i.forwardRef(function(e,t){return i.createElement(K,U({},e,{ref:t,sideCar:eE}))});eC.classNames=K.classNames;var eP=new WeakMap,ej=new WeakMap,eO={},eS=0,eA=function(e){return e&&(e.host||eA(e.parentNode))},eR=function(e,t,r,n){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=eA(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eO[r]||(eO[r]=new WeakMap);var a=eO[r],i=[],l=new Set,s=new Set(o),c=function(e){!e||l.has(e)||(l.add(e),c(e.parentNode))};o.forEach(c);var d=function(e){!e||s.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))d(e);else try{var t=e.getAttribute(n),o=null!==t&&"false"!==t,s=(eP.get(e)||0)+1,c=(a.get(e)||0)+1;eP.set(e,s),a.set(e,c),i.push(e),1===s&&o&&ej.set(e,!0),1===c&&e.setAttribute(r,"true"),o||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return d(t),l.clear(),eS++,function(){i.forEach(function(e){var t=eP.get(e)-1,o=a.get(e)-1;eP.set(e,t),a.set(e,o),t||(ej.has(e)||e.removeAttribute(n),ej.delete(e)),o||e.removeAttribute(r)}),--eS||(eP=new WeakMap,eP=new WeakMap,ej=new WeakMap,eO={})}},eN=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(n.push.apply(n,Array.from(o.querySelectorAll("[aria-live], script"))),eR(n,o,r,"aria-hidden")):function(){return null}},eL=r(9708),eM="Dialog",[e_,ez]=(0,d.A)(eM),[eI,eD]=e_(eM),eT=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:o,onOpenChange:a,modal:l=!0}=e,s=i.useRef(null),c=i.useRef(null),[d,u]=(0,h.i)({prop:n,defaultProp:null!=o&&o,onChange:a,caller:eM});return(0,b.jsx)(eI,{scope:t,triggerRef:s,contentRef:c,contentId:m(),titleId:m(),descriptionId:m(),open:d,onOpenChange:u,onOpenToggle:i.useCallback(()=>u(e=>!e),[u]),modal:l,children:r})};eT.displayName=eM;var eU="DialogTrigger";i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=eD(eU,r),a=(0,c.s)(t,o.triggerRef);return(0,b.jsx)(g.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":e3(o.open),...n,ref:a,onClick:(0,s.m)(e.onClick,o.onOpenToggle)})}).displayName=eU;var eF="DialogPortal",[e$,eW]=e_(eF,{forceMount:void 0}),eG=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:o}=e,a=eD(eF,t);return(0,b.jsx)(e$,{scope:t,forceMount:r,children:i.Children.map(n,e=>(0,b.jsx)(z,{present:r||a.open,children:(0,b.jsx)(_,{asChild:!0,container:o,children:e})}))})};eG.displayName=eF;var eB="DialogOverlay",eq=i.forwardRef((e,t)=>{let r=eW(eB,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=eD(eB,e.__scopeDialog);return a.modal?(0,b.jsx)(z,{present:n||a.open,children:(0,b.jsx)(eV,{...o,ref:t})}):null});eq.displayName=eB;var eH=(0,eL.TL)("DialogOverlay.RemoveScroll"),eV=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=eD(eB,r);return(0,b.jsx)(eC,{as:eH,allowPinchZoom:!0,shards:[o.contentRef],children:(0,b.jsx)(g.sG.div,{"data-state":e3(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),eX="DialogContent",eK=i.forwardRef((e,t)=>{let r=eW(eX,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=eD(eX,e.__scopeDialog);return(0,b.jsx)(z,{present:n||a.open,children:a.modal?(0,b.jsx)(eY,{...o,ref:t}):(0,b.jsx)(eZ,{...o,ref:t})})});eK.displayName=eX;var eY=i.forwardRef((e,t)=>{let r=eD(eX,e.__scopeDialog),n=i.useRef(null),o=(0,c.s)(t,r.contentRef,n);return i.useEffect(()=>{let e=n.current;if(e)return eN(e)},[]),(0,b.jsx)(eJ,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,s.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,s.m)(e.onFocusOutside,e=>e.preventDefault())})}),eZ=i.forwardRef((e,t)=>{let r=eD(eX,e.__scopeDialog),n=i.useRef(!1),o=i.useRef(!1);return(0,b.jsx)(eJ,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,i;null==(a=e.onCloseAutoFocus)||a.call(e,t),t.defaultPrevented||(n.current||null==(i=r.triggerRef.current)||i.focus(),t.preventDefault()),n.current=!1,o.current=!1},onInteractOutside:t=>{var a,i;null==(a=e.onInteractOutside)||a.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let l=t.target;(null==(i=r.triggerRef.current)?void 0:i.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),eJ=i.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:a,...l}=e,s=eD(eX,r),d=i.useRef(null),u=(0,c.s)(t,d);return i.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=r[0])?e:T()),document.body.insertAdjacentElement("beforeend",null!=(t=r[1])?t:T()),D++,()=>{1===D&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),D--}},[]),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(O,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,b.jsx)(x,{role:"dialog",id:s.contentId,"aria-describedby":s.descriptionId,"aria-labelledby":s.titleId,"data-state":e3(s.open),...l,ref:u,onDismiss:()=>s.onOpenChange(!1)})}),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(e7,{titleId:s.titleId}),(0,b.jsx)(te,{contentRef:d,descriptionId:s.descriptionId})]})]})}),eQ="DialogTitle",e0=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=eD(eQ,r);return(0,b.jsx)(g.sG.h2,{id:o.titleId,...n,ref:t})});e0.displayName=eQ;var e1="DialogDescription",e2=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=eD(e1,r);return(0,b.jsx)(g.sG.p,{id:o.descriptionId,...n,ref:t})});e2.displayName=e1;var e5="DialogClose",e4=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=eD(e5,r);return(0,b.jsx)(g.sG.button,{type:"button",...n,ref:t,onClick:(0,s.m)(e.onClick,()=>o.onOpenChange(!1))})});function e3(e){return e?"open":"closed"}e4.displayName=e5;var e6="DialogTitleWarning",[e8,e9]=(0,d.q)(e6,{contentName:eX,titleName:eQ,docsSlug:"dialog"}),e7=e=>{let{titleId:t}=e,r=e9(e6),n="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return i.useEffect(()=>{t&&(document.getElementById(t)||console.error(n))},[n,t]),null},te=e=>{let{contentRef:t,descriptionId:r}=e,n=e9("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(n.contentName,"}.");return i.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(o))},[o,t,r]),null},tt=eT,tr=eG,tn=eq,to=eK,ta=e0,ti=e2,tl=e4},4355:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},4416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4616:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4992:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("thermometer",[["path",{d:"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z",key:"17jzev"}]])},5029:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let n=r(2115),o=n.useLayoutEffect,a=n.useEffect;function i(e){let{headManager:t,reduceComponentsToState:r}=e;function i(){if(t&&t.mountedInstances){let o=n.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(o,e))}}return o(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),o(()=>(t&&(t._pendingUpdate=i),()=>{t&&(t._pendingUpdate=i)})),a(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},5100:(e,t)=>{function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:o,blurDataURL:a,objectFit:i}=e,l=n?40*n:t,s=o?40*o:r,c=l&&s?"viewBox='0 0 "+l+" "+s+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(c?"none":"contain"===i?"xMidYMid":"cover"===i?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},5169:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},5564:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return h},defaultHead:function(){return u}});let n=r(8229),o=r(6966),a=r(5155),i=o._(r(2115)),l=n._(r(5029)),s=r(2464),c=r(2830),d=r(7544);function u(e){void 0===e&&(e=!1);let t=[(0,a.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===i.default.Fragment?e.concat(i.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(3230);let p=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:r}=t;return e.reduce(f,[]).reverse().concat(u(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return o=>{let a=!0,i=!1;if(o.key&&"number"!=typeof o.key&&o.key.indexOf("$")>0){i=!0;let t=o.key.slice(o.key.indexOf("$")+1);e.has(t)?a=!1:e.add(t)}switch(o.type){case"title":case"base":t.has(o.type)?a=!1:t.add(o.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(o.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?a=!1:r.add(t);else{let e=o.props[t],r=n[t]||new Set;("name"!==t||!i)&&r.has(e)?a=!1:(r.add(e),n[t]=r)}}}return a}}()).reverse().map((e,t)=>{let r=e.key||t;return i.default.cloneElement(e,{key:r})})}let h=function(e){let{children:t}=e,r=(0,i.useContext)(s.AmpStateContext),n=(0,i.useContext)(c.HeadManagerContext);return(0,a.jsx)(l.default,{reduceComponentsToState:m,headManager:n,inAmpMode:(0,d.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5840:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},5845:(e,t,r)=>{r.d(t,{i:()=>l});var n,o=r(2115),a=r(2712),i=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||a.N;function l({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[a,l,s]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),a=o.useRef(r),l=o.useRef(t);return i(()=>{l.current=t},[t]),o.useEffect(()=>{a.current!==r&&(l.current?.(r),a.current=r)},[r,a]),[r,n,l]}({defaultProp:t,onChange:r}),c=void 0!==e,d=c?e:a;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,n])}return[d,o.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&s.current?.(r)}else l(t)},[c,e,l,s])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,r)=>{r.d(t,{A:()=>i,q:()=>a});var n=r(2115),o=r(5155);function a(e,t){let r=n.createContext(t),a=e=>{let{children:t,...a}=e,i=n.useMemo(()=>a,Object.values(a));return(0,o.jsx)(r.Provider,{value:i,children:t})};return a.displayName=e+"Provider",[a,function(o){let a=n.useContext(r);if(a)return a;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return a.scopeName=e,[function(t,a){let i=n.createContext(a),l=r.length;r=[...r,a];let s=t=>{let{scope:r,children:a,...s}=t,c=r?.[e]?.[l]||i,d=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(c.Provider,{value:d,children:a})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[l]||i,c=n.useContext(s);if(c)return c;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(a,...t)]}},6101:(e,t,r)=>{r.d(t,{s:()=>i,t:()=>a});var n=r(2115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(a(...e),e)}},6654:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(2115);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=a(e,n)),t&&(o.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6683:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("share",[["path",{d:"M12 2v13",key:"1km8f5"}],["path",{d:"m16 6-4-4-4 4",key:"13yo43"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}]])},6752:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return a}});let n=r(8229)._(r(2115)),o=r(5840),a=n.default.createContext(o.imageConfigDefault)},6766:(e,t,r)=>{r.d(t,{default:()=>o.a});var n=r(1469),o=r.n(n)},7213:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7340:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},7544:(e,t)=>{function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},8611:(e,t,r)=>{r.d(t,{bL:()=>x,zi:()=>k});var n=r(2115),o=r(5185),a=r(6101),i=r(6081),l=r(5845),s=r(2712),c=r(3655),d=r(5155),u="Switch",[f,p]=(0,i.A)(u),[m,h]=f(u),g=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:i,checked:s,defaultChecked:f,required:p,disabled:h,value:g="on",onCheckedChange:v,form:b,...x}=e,[k,E]=n.useState(null),C=(0,a.s)(t,e=>E(e)),P=n.useRef(!1),j=!k||b||!!k.closest("form"),[O,S]=(0,l.i)({prop:s,defaultProp:null!=f&&f,onChange:v,caller:u});return(0,d.jsxs)(m,{scope:r,checked:O,disabled:h,children:[(0,d.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":O,"aria-required":p,"data-state":w(O),"data-disabled":h?"":void 0,disabled:h,value:g,...x,ref:C,onClick:(0,o.m)(e.onClick,e=>{S(e=>!e),j&&(P.current=e.isPropagationStopped(),P.current||e.stopPropagation())})}),j&&(0,d.jsx)(y,{control:k,bubbles:!P.current,name:i,value:g,checked:O,required:p,disabled:h,form:b,style:{transform:"translateX(-100%)"}})]})});g.displayName=u;var v="SwitchThumb",b=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=h(v,r);return(0,d.jsx)(c.sG.span,{"data-state":w(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});b.displayName=v;var y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:o,checked:i,bubbles:l=!0,...c}=e,u=n.useRef(null),f=(0,a.s)(u,t),p=function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(i),m=function(e){let[t,r]=n.useState(void 0);return(0,s.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(o);return n.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==i&&t){let r=new Event("click",{bubbles:l});t.call(e,i),e.dispatchEvent(r)}},[p,i,l]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...c,tabIndex:-1,ref:f,style:{...c.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(e){return e?"checked":"unchecked"}y.displayName="SwitchBubbleInput";var x=g,k=b},8691:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]])},8883:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return s}}),r(3230);let n=r(5100),o=r(5840),a=["-moz-initial","fill","none","scale-down",void 0];function i(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function s(e,t){var r,s;let c,d,u,{src:f,sizes:p,unoptimized:m=!1,priority:h=!1,loading:g,className:v,quality:b,width:y,height:w,fill:x=!1,style:k,overrideSrc:E,onLoad:C,onLoadingComplete:P,placeholder:j="empty",blurDataURL:O,fetchPriority:S,decoding:A="async",layout:R,objectFit:N,objectPosition:L,lazyBoundary:M,lazyRoot:_,...z}=e,{imgConf:I,showAltText:D,blurComplete:T,defaultLoader:U}=t,F=I||o.imageConfigDefault;if("allSizes"in F)c=F;else{let e=[...F.deviceSizes,...F.imageSizes].sort((e,t)=>e-t),t=F.deviceSizes.sort((e,t)=>e-t),n=null==(r=F.qualities)?void 0:r.sort((e,t)=>e-t);c={...F,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===U)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let $=z.loader||U;delete z.loader,delete z.srcSet;let W="__next_img_default"in $;if(W){if("custom"===c.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=$;$=t=>{let{config:r,...n}=t;return e(n)}}if(R){"fill"===R&&(x=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(k={...k,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!p&&(p=t)}let G="",B=l(y),q=l(w);if((s=f)&&"object"==typeof s&&(i(s)||void 0!==s.src)){let e=i(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,u=e.blurHeight,O=O||e.blurDataURL,G=e.src,!x)if(B||q){if(B&&!q){let t=B/e.width;q=Math.round(e.height*t)}else if(!B&&q){let t=q/e.height;B=Math.round(e.width*t)}}else B=e.width,q=e.height}let H=!h&&("lazy"===g||void 0===g);(!(f="string"==typeof f?f:G)||f.startsWith("data:")||f.startsWith("blob:"))&&(m=!0,H=!1),c.unoptimized&&(m=!0),W&&!c.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(m=!0);let V=l(b),X=Object.assign(x?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:N,objectPosition:L}:{},D?{}:{color:"transparent"},k),K=T||"empty"===j?null:"blur"===j?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:B,heightInt:q,blurWidth:d,blurHeight:u,blurDataURL:O||"",objectFit:X.objectFit})+'")':'url("'+j+'")',Y=a.includes(X.objectFit)?"fill"===X.objectFit?"100% 100%":"cover":X.objectFit,Z=K?{backgroundSize:Y,backgroundPosition:X.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:K}:{},J=function(e){let{config:t,src:r,unoptimized:n,width:o,quality:a,sizes:i,loader:l}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:s,kind:c}=function(e,t,r){let{deviceSizes:n,allSizes:o}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:o.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:o,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>o.find(t=>t>=e)||o[o.length-1]))],kind:"x"}}(t,o,i),d=s.length-1;return{sizes:i||"w"!==c?i:"100vw",srcSet:s.map((e,n)=>l({config:t,src:r,quality:a,width:e})+" "+("w"===c?e:n+1)+c).join(", "),src:l({config:t,src:r,quality:a,width:s[d]})}}({config:c,src:f,unoptimized:m,width:B,quality:V,sizes:p,loader:$});return{props:{...z,loading:H?"lazy":g,fetchPriority:S,width:B,height:q,decoding:A,className:v,style:{...X,...Z},sizes:J.sizes,srcSet:J.srcSet,src:E||J.src},meta:{unoptimized:m,priority:h,placeholder:j,fill:x}}}},9348:(e,t,r)=>{var n,o,a,i;r.d(t,{i7:()=>v,LK:()=>i,ru:()=>o}),function(e){e.Unimplemented="UNIMPLEMENTED",e.Unavailable="UNAVAILABLE"}(n||(n={}));class l extends Error{constructor(e,t,r){super(e),this.message=e,this.code=t,this.data=r}}let s=(e=>e.Capacitor=(e=>{let t=e.CapacitorCustomPlatform||null,r=e.Capacitor||{},o=r.Plugins=r.Plugins||{},a=()=>null!==t?t.name:(e=>{var t,r;return(null==e?void 0:e.androidBridge)?"android":(null==(r=null==(t=null==e?void 0:e.webkit)?void 0:t.messageHandlers)?void 0:r.bridge)?"ios":"web"})(e),i=e=>{var t;return null==(t=r.PluginHeaders)?void 0:t.find(t=>t.name===e)},s=new Map;return r.convertFileSrc||(r.convertFileSrc=e=>e),r.getPlatform=a,r.handleError=t=>e.console.error(t),r.isNativePlatform=()=>"web"!==a(),r.isPluginAvailable=e=>{let t=s.get(e);return!!((null==t?void 0:t.platforms.has(a()))||i(e))},r.registerPlugin=(e,c={})=>{let d,u=s.get(e);if(u)return console.warn(`Capacitor plugin "${e}" already registered. Cannot register plugins twice.`),u.proxy;let f=a(),p=i(e),m=async()=>(!d&&f in c?d=d="function"==typeof c[f]?await c[f]():c[f]:null!==t&&!d&&"web"in c&&(d=d="function"==typeof c.web?await c.web():c.web),d),h=t=>{let o,a=(...a)=>{let i=m().then(i=>{let s=((t,o)=>{var a,i;if(p){let n=null==p?void 0:p.methods.find(e=>o===e.name);if(n)if("promise"===n.rtype)return t=>r.nativePromise(e,o.toString(),t);else return(t,n)=>r.nativeCallback(e,o.toString(),t,n);if(t)return null==(a=t[o])?void 0:a.bind(t)}else if(t)return null==(i=t[o])?void 0:i.bind(t);else throw new l(`"${e}" plugin is not implemented on ${f}`,n.Unimplemented)})(i,t);if(s){let e=s(...a);return o=null==e?void 0:e.remove,e}throw new l(`"${e}.${t}()" is not implemented on ${f}`,n.Unimplemented)});return"addListener"===t&&(i.remove=async()=>o()),i};return a.toString=()=>`${t.toString()}() { [capacitor code] }`,Object.defineProperty(a,"name",{value:t,writable:!1,configurable:!1}),a},g=h("addListener"),v=h("removeListener"),b=(e,t)=>{let r=g({eventName:e},t),n=async()=>{v({eventName:e,callbackId:await r},t)},o=new Promise(e=>r.then(()=>e({remove:n})));return o.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await n()},o},y=new Proxy({},{get(e,t){switch(t){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return p?b:g;case"removeListener":return v;default:return h(t)}}});return o[e]=y,s.set(e,{name:e,proxy:y,platforms:new Set([...Object.keys(c),...p?[f]:[]])}),y},r.Exception=l,r.DEBUG=!!r.DEBUG,r.isLoggingEnabled=!!r.isLoggingEnabled,r})(e))("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==r.g?r.g:{}),c=s.registerPlugin;class d{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,t){let r=!1;this.listeners[e]||(this.listeners[e]=[],r=!0),this.listeners[e].push(t);let n=this.windowListeners[e];return n&&!n.registered&&this.addWindowListener(n),r&&this.sendRetainedArgumentsForEvent(e),Promise.resolve({remove:async()=>this.removeListener(e,t)})}async removeAllListeners(){for(let e in this.listeners={},this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,r){let n=this.listeners[e];if(!n){if(r){let r=this.retainedEventArguments[e];r||(r=[]),r.push(t),this.retainedEventArguments[e]=r}return}n.forEach(e=>e(t))}hasListeners(e){var t;return!!(null==(t=this.listeners[e])?void 0:t.length)}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:e=>{this.notifyListeners(t,e)}}}unimplemented(e="not implemented"){return new s.Exception(e,n.Unimplemented)}unavailable(e="not available"){return new s.Exception(e,n.Unavailable)}async removeListener(e,t){let r=this.listeners[e];if(!r)return;let n=r.indexOf(t);this.listeners[e].splice(n,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){let t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(t=>{this.notifyListeners(e,t)}))}}let u=e=>encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),f=e=>e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class p extends d{async getCookies(){let e=document.cookie,t={};return e.split(";").forEach(e=>{if(e.length<=0)return;let[r,n]=e.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");r=f(r).trim(),n=f(n).trim(),t[r]=n}),t}async setCookie(e){try{let t=u(e.key),r=u(e.value),n=`; expires=${(e.expires||"").replace("expires=","")}`,o=(e.path||"/").replace("path=",""),a=null!=e.url&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${r||""}${n}; path=${o}; ${a};`}catch(e){return Promise.reject(e)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(e){return Promise.reject(e)}}async clearCookies(){try{for(let e of document.cookie.split(";")||[])document.cookie=e.replace(/^ +/,"").replace(/=.*/,`=;expires=${new Date().toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}c("CapacitorCookies",{web:()=>new p});let m=async e=>new Promise((t,r)=>{let n=new FileReader;n.onload=()=>{let e=n.result;t(e.indexOf(",")>=0?e.split(",")[1]:e)},n.onerror=e=>r(e),n.readAsDataURL(e)});class h extends d{async request(e){let t,r,n=((e,t={})=>{let r=Object.assign({method:e.method||"GET",headers:e.headers},t),n=((e={})=>{let t=Object.keys(e);return Object.keys(e).map(e=>e.toLocaleLowerCase()).reduce((r,n,o)=>(r[n]=e[t[o]],r),{})})(e.headers)["content-type"]||"";if("string"==typeof e.data)r.body=e.data;else if(n.includes("application/x-www-form-urlencoded")){let t=new URLSearchParams;for(let[r,n]of Object.entries(e.data||{}))t.set(r,n);r.body=t.toString()}else if(n.includes("multipart/form-data")||e.data instanceof FormData){let t=new FormData;if(e.data instanceof FormData)e.data.forEach((e,r)=>{t.append(r,e)});else for(let r of Object.keys(e.data))t.append(r,e.data[r]);r.body=t;let n=new Headers(r.headers);n.delete("content-type"),r.headers=n}else(n.includes("application/json")||"object"==typeof e.data)&&(r.body=JSON.stringify(e.data));return r})(e,e.webFetchExtra),o=((e,t=!0)=>e?Object.entries(e).reduce((e,r)=>{let n,o,[a,i]=r;return Array.isArray(i)?(o="",i.forEach(e=>{n=t?encodeURIComponent(e):e,o+=`${a}=${n}&`}),o.slice(0,-1)):(n=t?encodeURIComponent(i):i,o=`${a}=${n}`),`${e}&${o}`},"").substr(1):null)(e.params,e.shouldEncodeUrlParams),a=o?`${e.url}?${o}`:e.url,i=await fetch(a,n),l=i.headers.get("content-type")||"",{responseType:s="text"}=i.ok?e:{};switch(l.includes("application/json")&&(s="json"),s){case"arraybuffer":case"blob":r=await i.blob(),t=await m(r);break;case"json":t=await i.json();break;default:t=await i.text()}let c={};return i.headers.forEach((e,t)=>{c[t]=e}),{data:t,headers:c,status:i.status,url:i.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}c("CapacitorHttp",{web:()=>new h}),function(e){e.Prompt="PROMPT",e.Camera="CAMERA",e.Photos="PHOTOS"}(o||(o={})),function(e){e.Rear="REAR",e.Front="FRONT"}(a||(a={})),function(e){e.Uri="uri",e.Base64="base64",e.DataUrl="dataUrl"}(i||(i={}));class g extends d{async getPhoto(e){return new Promise(async(t,r)=>{if(e.webUseInput||e.source===o.Photos)this.fileInputExperience(e,t,r);else if(e.source===o.Prompt){let n=document.querySelector("pwa-action-sheet");n||(n=document.createElement("pwa-action-sheet"),document.body.appendChild(n)),n.header=e.promptLabelHeader||"Photo",n.cancelable=!1,n.options=[{title:e.promptLabelPhoto||"From Photos"},{title:e.promptLabelPicture||"Take Picture"}],n.addEventListener("onSelection",async n=>{0===n.detail?this.fileInputExperience(e,t,r):this.cameraExperience(e,t,r)})}else this.cameraExperience(e,t,r)})}async pickImages(e){return new Promise(async(e,t)=>{this.multipleFileInputExperience(e,t)})}async cameraExperience(e,t,r){if(customElements.get("pwa-camera-modal")){let n=document.createElement("pwa-camera-modal");n.facingMode=e.direction===a.Front?"user":"environment",document.body.appendChild(n);try{await n.componentOnReady(),n.addEventListener("onPhoto",async o=>{let a=o.detail;null===a?r(new l("User cancelled photos app")):a instanceof Error?r(a):t(await this._getCameraPhoto(a,e)),n.dismiss(),document.body.removeChild(n)}),n.present()}catch(n){this.fileInputExperience(e,t,r)}}else console.error("Unable to load PWA Element 'pwa-camera-modal'. See the docs: https://capacitorjs.com/docs/web/pwa-elements."),this.fileInputExperience(e,t,r)}fileInputExperience(e,t,r){let n=document.querySelector("#_capacitor-camera-input"),i=()=>{var e;null==(e=n.parentNode)||e.removeChild(n)};n||((n=document.createElement("input")).id="_capacitor-camera-input",n.type="file",n.hidden=!0,document.body.appendChild(n),n.addEventListener("change",r=>{let o=n.files[0],a="jpeg";if("image/png"===o.type?a="png":"image/gif"===o.type&&(a="gif"),"dataUrl"===e.resultType||"base64"===e.resultType){let r=new FileReader;r.addEventListener("load",()=>{"dataUrl"===e.resultType?t({dataUrl:r.result,format:a}):"base64"===e.resultType&&t({base64String:r.result.split(",")[1],format:a}),i()}),r.readAsDataURL(o)}else t({webPath:URL.createObjectURL(o),format:a}),i()}),n.addEventListener("cancel",e=>{r(new l("User cancelled photos app")),i()})),n.accept="image/*",n.capture=!0,e.source===o.Photos||e.source===o.Prompt?n.removeAttribute("capture"):e.direction===a.Front?n.capture="user":e.direction===a.Rear&&(n.capture="environment"),n.click()}multipleFileInputExperience(e,t){let r=document.querySelector("#_capacitor-camera-input-multiple"),n=()=>{var e;null==(e=r.parentNode)||e.removeChild(r)};r||((r=document.createElement("input")).id="_capacitor-camera-input-multiple",r.type="file",r.hidden=!0,r.multiple=!0,document.body.appendChild(r),r.addEventListener("change",t=>{let o=[];for(let e=0;e<r.files.length;e++){let t=r.files[e],n="jpeg";"image/png"===t.type?n="png":"image/gif"===t.type&&(n="gif"),o.push({webPath:URL.createObjectURL(t),format:n})}e({photos:o}),n()}),r.addEventListener("cancel",e=>{t(new l("User cancelled photos app")),n()})),r.accept="image/*",r.click()}_getCameraPhoto(e,t){return new Promise((r,n)=>{let o=new FileReader,a=e.type.split("/")[1];"uri"===t.resultType?r({webPath:URL.createObjectURL(e),format:a,saved:!1}):(o.readAsDataURL(e),o.onloadend=()=>{let e=o.result;"dataUrl"===t.resultType?r({dataUrl:e,format:a,saved:!1}):r({base64String:e.split(",")[1],format:a,saved:!1})},o.onerror=e=>{n(e)})})}async checkPermissions(){if("undefined"==typeof navigator||!navigator.permissions)throw this.unavailable("Permissions API not available in this browser");try{return{camera:(await window.navigator.permissions.query({name:"camera"})).state,photos:"granted"}}catch(e){throw this.unavailable("Camera permissions are not available in this browser")}}async requestPermissions(){throw this.unimplemented("Not implemented on web.")}async pickLimitedLibraryPhotos(){throw this.unavailable("Not implemented on web.")}async getLimitedLibraryPhotos(){throw this.unavailable("Not implemented on web.")}}new g;let v=c("Camera",{web:()=>new g})},9688:(e,t,r)=>{r.d(t,{QP:()=>ee});let n=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],o=t.nextPart.get(r),a=o?n(e.slice(1),o):void 0;if(a)return a;if(0===t.validators.length)return;let i=e.join("-");return t.validators.find(({validator:e})=>e(i))?.classGroupId},o=/^\[(.+)\]$/,a=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:i(t,e)).classGroupId=r;return}if("function"==typeof e)return l(e)?void a(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,o])=>{a(o,i(t,e),r,n)})})},i=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},l=e=>e.isThemeGetter,s=/\s+/;function c(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=d(e))&&(n&&(n+=" "),n+=t);return n}let d=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=d(e[n]))&&(r&&(r+=" "),r+=t);return r},u=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},f=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,p=/^\((?:(\w[\w-]*):)?(.+)\)$/i,m=/^\d+\/\d+$/,h=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,v=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,b=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,y=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,w=e=>m.test(e),x=e=>!!e&&!Number.isNaN(Number(e)),k=e=>!!e&&Number.isInteger(Number(e)),E=e=>e.endsWith("%")&&x(e.slice(0,-1)),C=e=>h.test(e),P=()=>!0,j=e=>g.test(e)&&!v.test(e),O=()=>!1,S=e=>b.test(e),A=e=>y.test(e),R=e=>!L(e)&&!T(e),N=e=>q(e,K,O),L=e=>f.test(e),M=e=>q(e,Y,j),_=e=>q(e,Z,x),z=e=>q(e,V,O),I=e=>q(e,X,A),D=e=>q(e,Q,S),T=e=>p.test(e),U=e=>H(e,Y),F=e=>H(e,J),$=e=>H(e,V),W=e=>H(e,K),G=e=>H(e,X),B=e=>H(e,Q,!0),q=(e,t,r)=>{let n=f.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},H=(e,t,r=!1)=>{let n=p.exec(e);return!!n&&(n[1]?t(n[1]):r)},V=e=>"position"===e||"percentage"===e,X=e=>"image"===e||"url"===e,K=e=>"length"===e||"size"===e||"bg-size"===e,Y=e=>"length"===e,Z=e=>"number"===e,J=e=>"family-name"===e,Q=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...t){let r,i,l,d=function(s){let c;return i=(r={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,a)=>{r.set(o,a),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}})((c=t.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r,n=[],o=0,a=0,i=0;for(let r=0;r<e.length;r++){let l=e[r];if(0===o&&0===a){if(":"===l){n.push(e.slice(i,r)),i=r+1;continue}if("/"===l){t=r;continue}}"["===l?o++:"]"===l?o--:"("===l?a++:")"===l&&a--}let l=0===n.length?e:e.substring(i),s=(r=l).endsWith("!")?r.substring(0,r.length-1):r.startsWith("!")?r.substring(1):r;return{modifiers:n,hasImportantModifier:s!==l,baseClassName:s,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n})(c),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}})(c),...(e=>{let t=(e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)a(r[e],n,e,t);return n})(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:i}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,t)||(e=>{if(o.test(e)){let t=o.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}})(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&i[e]?[...n,...i[e]]:n}}})(c)}).cache.get,l=r.cache.set,d=u,u(s)};function u(e){let t=i(e);if(t)return t;let n=((e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:a}=t,i=[],l=e.trim().split(s),c="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{isExternal:s,modifiers:d,hasImportantModifier:u,baseClassName:f,maybePostfixModifierPosition:p}=r(t);if(s){c=t+(c.length>0?" "+c:c);continue}let m=!!p,h=n(m?f.substring(0,p):f);if(!h){if(!m||!(h=n(f))){c=t+(c.length>0?" "+c:c);continue}m=!1}let g=a(d).join(":"),v=u?g+"!":g,b=v+h;if(i.includes(b))continue;i.push(b);let y=o(h,m);for(let e=0;e<y.length;++e){let t=y[e];i.push(v+t)}c=t+(c.length>0?" "+c:c)}return c})(e,r);return l(e,n),n}return function(){return d(c.apply(null,arguments))}}(()=>{let e=u("color"),t=u("font"),r=u("text"),n=u("font-weight"),o=u("tracking"),a=u("leading"),i=u("breakpoint"),l=u("container"),s=u("spacing"),c=u("radius"),d=u("shadow"),f=u("inset-shadow"),p=u("text-shadow"),m=u("drop-shadow"),h=u("blur"),g=u("perspective"),v=u("aspect"),b=u("ease"),y=u("animate"),j=()=>["auto","avoid","all","avoid-page","page","left","right","column"],O=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],S=()=>[...O(),T,L],A=()=>["auto","hidden","clip","visible","scroll"],q=()=>["auto","contain","none"],H=()=>[T,L,s],V=()=>[w,"full","auto",...H()],X=()=>[k,"none","subgrid",T,L],K=()=>["auto",{span:["full",k,T,L]},k,T,L],Y=()=>[k,"auto",T,L],Z=()=>["auto","min","max","fr",T,L],J=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Q=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...H()],et=()=>[w,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...H()],er=()=>[e,T,L],en=()=>[...O(),$,z,{position:[T,L]}],eo=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",W,N,{size:[T,L]}],ei=()=>[E,U,M],el=()=>["","none","full",c,T,L],es=()=>["",x,U,M],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[x,E,$,z],ef=()=>["","none",h,T,L],ep=()=>["none",x,T,L],em=()=>["none",x,T,L],eh=()=>[x,T,L],eg=()=>[w,"full",...H()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[C],breakpoint:[C],color:[P],container:[C],"drop-shadow":[C],ease:["in","out","in-out"],font:[R],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[C],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[C],shadow:[C],spacing:["px",x],text:[C],"text-shadow":[C],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",w,L,T,v]}],container:["container"],columns:[{columns:[x,L,T,l]}],"break-after":[{"break-after":j()}],"break-before":[{"break-before":j()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:S()}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:q()}],"overscroll-x":[{"overscroll-x":q()}],"overscroll-y":[{"overscroll-y":q()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:V()}],"inset-x":[{"inset-x":V()}],"inset-y":[{"inset-y":V()}],start:[{start:V()}],end:[{end:V()}],top:[{top:V()}],right:[{right:V()}],bottom:[{bottom:V()}],left:[{left:V()}],visibility:["visible","invisible","collapse"],z:[{z:[k,"auto",T,L]}],basis:[{basis:[w,"full","auto",l,...H()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[x,w,"auto","initial","none",L]}],grow:[{grow:["",x,T,L]}],shrink:[{shrink:["",x,T,L]}],order:[{order:[k,"first","last","none",T,L]}],"grid-cols":[{"grid-cols":X()}],"col-start-end":[{col:K()}],"col-start":[{"col-start":Y()}],"col-end":[{"col-end":Y()}],"grid-rows":[{"grid-rows":X()}],"row-start-end":[{row:K()}],"row-start":[{"row-start":Y()}],"row-end":[{"row-end":Y()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:H()}],"gap-x":[{"gap-x":H()}],"gap-y":[{"gap-y":H()}],"justify-content":[{justify:[...J(),"normal"]}],"justify-items":[{"justify-items":[...Q(),"normal"]}],"justify-self":[{"justify-self":["auto",...Q()]}],"align-content":[{content:["normal",...J()]}],"align-items":[{items:[...Q(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Q(),{baseline:["","last"]}]}],"place-content":[{"place-content":J()}],"place-items":[{"place-items":[...Q(),"baseline"]}],"place-self":[{"place-self":["auto",...Q()]}],p:[{p:H()}],px:[{px:H()}],py:[{py:H()}],ps:[{ps:H()}],pe:[{pe:H()}],pt:[{pt:H()}],pr:[{pr:H()}],pb:[{pb:H()}],pl:[{pl:H()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":H()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":H()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[l,"screen",...et()]}],"min-w":[{"min-w":[l,"screen","none",...et()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[i]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,U,M]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,T,_]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",E,L]}],"font-family":[{font:[F,L,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,T,L]}],"line-clamp":[{"line-clamp":[x,"none",T,_]}],leading:[{leading:[a,...H()]}],"list-image":[{"list-image":["none",T,L]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",T,L]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[x,"from-font","auto",T,M]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[x,"auto",T,L]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:H()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",T,L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",T,L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:eo()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},k,T,L],radial:["",T,L],conic:[k,T,L]},G,I]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ei()}],"gradient-via-pos":[{via:ei()}],"gradient-to-pos":[{to:ei()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:es()}],"border-w-x":[{"border-x":es()}],"border-w-y":[{"border-y":es()}],"border-w-s":[{"border-s":es()}],"border-w-e":[{"border-e":es()}],"border-w-t":[{"border-t":es()}],"border-w-r":[{"border-r":es()}],"border-w-b":[{"border-b":es()}],"border-w-l":[{"border-l":es()}],"divide-x":[{"divide-x":es()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":es()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[x,T,L]}],"outline-w":[{outline:["",x,U,M]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",d,B,D]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",f,B,D]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:es()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[x,M]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":es()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",p,B,D]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[x,T,L]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[x]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[T,L]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":O()}],"mask-image-conic-pos":[{"mask-conic":[x]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:eo()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",T,L]}],filter:[{filter:["","none",T,L]}],blur:[{blur:ef()}],brightness:[{brightness:[x,T,L]}],contrast:[{contrast:[x,T,L]}],"drop-shadow":[{"drop-shadow":["","none",m,B,D]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",x,T,L]}],"hue-rotate":[{"hue-rotate":[x,T,L]}],invert:[{invert:["",x,T,L]}],saturate:[{saturate:[x,T,L]}],sepia:[{sepia:["",x,T,L]}],"backdrop-filter":[{"backdrop-filter":["","none",T,L]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[x,T,L]}],"backdrop-contrast":[{"backdrop-contrast":[x,T,L]}],"backdrop-grayscale":[{"backdrop-grayscale":["",x,T,L]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[x,T,L]}],"backdrop-invert":[{"backdrop-invert":["",x,T,L]}],"backdrop-opacity":[{"backdrop-opacity":[x,T,L]}],"backdrop-saturate":[{"backdrop-saturate":[x,T,L]}],"backdrop-sepia":[{"backdrop-sepia":["",x,T,L]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":H()}],"border-spacing-x":[{"border-spacing-x":H()}],"border-spacing-y":[{"border-spacing-y":H()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",T,L]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[x,"initial",T,L]}],ease:[{ease:["linear","initial",b,T,L]}],delay:[{delay:[x,T,L]}],animate:[{animate:["none",y,T,L]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,T,L]}],"perspective-origin":[{"perspective-origin":S()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:eh()}],"skew-x":[{"skew-x":eh()}],"skew-y":[{"skew-y":eh()}],transform:[{transform:[T,L,"","none","gpu","cpu"]}],"transform-origin":[{origin:S()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",T,L]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":H()}],"scroll-mx":[{"scroll-mx":H()}],"scroll-my":[{"scroll-my":H()}],"scroll-ms":[{"scroll-ms":H()}],"scroll-me":[{"scroll-me":H()}],"scroll-mt":[{"scroll-mt":H()}],"scroll-mr":[{"scroll-mr":H()}],"scroll-mb":[{"scroll-mb":H()}],"scroll-ml":[{"scroll-ml":H()}],"scroll-p":[{"scroll-p":H()}],"scroll-px":[{"scroll-px":H()}],"scroll-py":[{"scroll-py":H()}],"scroll-ps":[{"scroll-ps":H()}],"scroll-pe":[{"scroll-pe":H()}],"scroll-pt":[{"scroll-pt":H()}],"scroll-pr":[{"scroll-pr":H()}],"scroll-pb":[{"scroll-pb":H()}],"scroll-pl":[{"scroll-pl":H()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",T,L]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[x,U,M,_]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9708:(e,t,r)=>{r.d(t,{DX:()=>l,TL:()=>i});var n=r(2115),o=r(6101),a=r(5155);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){var i;let e,l,s=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let r={...t};for(let n in t){let o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...e)=>{let t=a(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...a}:"className"===n&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,o.t)(t,s):s),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...i}=e,l=n.Children.toArray(o),s=l.find(c);if(s){let e=s.props.children,o=l.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...i,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),s=Symbol("radix.slottable");function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},9946:(e,t,r)=>{r.d(t,{A:()=>s});var n=r(2115);let o=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:s,className:c="",children:d,iconNode:u,...f}=e;return(0,n.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:r,strokeWidth:s?24*Number(l)/Number(o):l,className:a("lucide",c),...!d&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"},...f},[...u.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),s=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:s,...c}=r;return(0,n.createElement)(l,{ref:i,iconNode:t,className:a("lucide-".concat(o(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),s),...c})});return r.displayName=o(e),r}}}]);