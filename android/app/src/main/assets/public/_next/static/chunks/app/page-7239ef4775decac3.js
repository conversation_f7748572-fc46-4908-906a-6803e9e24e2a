(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{4884:(e,t,a)=>{"use strict";a.d(t,{default:()=>ee});var s=a(5155),r=a(2115),l=a(4355),i=a(7213),n=a(1154),c=a(1007),d=a(5169),o=a(6683),m=a(2098),x=a(8691),h=a(4992),u=a(4616),g=a(381),f=a(2138),p=a(7340),b=a(3384),v=a(9708),j=a(2085),y=a(2596),w=a(9688);function N(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,w.QP)((0,y.$)(t))}let k=(0,j.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function C(e){let{className:t,variant:a,size:r,asChild:l=!1,...i}=e,n=l?v.DX:"button";return(0,s.jsx)(n,{"data-slot":"button",className:N(k({variant:a,size:r,className:t})),...i})}var P=a(6766),z=a(4036),S=a(4416);function A(e){let{...t}=e;return(0,s.jsx)(z.bL,{"data-slot":"dialog",...t})}function D(e){let{...t}=e;return(0,s.jsx)(z.ZL,{"data-slot":"dialog-portal",...t})}function F(e){let{className:t,...a}=e;return(0,s.jsx)(z.hJ,{"data-slot":"dialog-overlay",className:N("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function E(e){let{className:t,children:a,showCloseButton:r=!0,...l}=e;return(0,s.jsxs)(D,{"data-slot":"dialog-portal",children:[(0,s.jsx)(F,{}),(0,s.jsxs)(z.UC,{"data-slot":"dialog-content",className:N("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...l,children:[a,r&&(0,s.jsxs)(z.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(S.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function L(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:N("flex flex-col gap-2 text-center sm:text-left",t),...a})}function I(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:N("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function R(e){let{className:t,...a}=e;return(0,s.jsx)(z.hE,{"data-slot":"dialog-title",className:N("text-lg leading-none font-semibold",t),...a})}function U(e){let{className:t,...a}=e;return(0,s.jsx)(z.VY,{"data-slot":"dialog-description",className:N("text-muted-foreground text-sm",t),...a})}var _=a(968);function T(e){let{className:t,...a}=e;return(0,s.jsx)(_.b,{"data-slot":"label",className:N("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}function M(e){let{className:t,type:a,...r}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:N("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...r})}function O(e){let{className:t,...a}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:N("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}var W=a(8611);function q(e){let{className:t,...a}=e;return(0,s.jsx)(W.bL,{"data-slot":"switch",className:N("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,s.jsx)(W.zi,{"data-slot":"switch-thumb",className:N("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}var H=a(9348);class V extends Error{constructor(e,t){super(e),this.statusCode=t,this.name="OpenRouterError"}}async function Y(e){return new Promise((t,a)=>{let s=new FileReader;s.onload=()=>{"string"==typeof s.result?t(s.result):a(Error("Failed to convert image to base64"))},s.onerror=()=>a(Error("Failed to read image file")),s.readAsDataURL(e)})}async function B(e,t){try{let a=await fetch("/api/openrouter/".concat(e),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){let e=await a.text();throw new V("API error: ".concat(a.status," ").concat(a.statusText," - ").concat(e),a.status)}return await a.json()}catch(e){if(e instanceof V)throw e;throw new V("Network error: ".concat(e instanceof Error?e.message:"Unknown error"))}}async function J(e){try{return await B("identify",{imageDataUrl:e})}catch(e){if(e instanceof V)throw e;throw new V("Failed to identify plant: ".concat(e instanceof Error?e.message:"Unknown error"))}}async function K(e,t){try{return await B("diagnose",{imageDataUrl:e,symptoms:t})}catch(e){if(e instanceof V)throw e;throw new V("Failed to diagnose plant: ".concat(e instanceof Error?e.message:"Unknown error"))}}async function Q(){try{let e=await H.i7.getPhoto({quality:90,allowEditing:!1,resultType:H.LK.DataUrl,source:H.ru.Camera});if(e.dataUrl){let t=await fetch(e.dataUrl),a=await t.blob();return new File([a],"camera-image.jpg",{type:"image/jpeg"})}return null}catch(e){return console.error("Error capturing image with Capacitor Camera:",e),X()}}async function X(){return new Promise(e=>{let t=document.createElement("input");t.type="file",t.accept="image/*",t.capture="environment",t.onchange=t=>{var a;e((null==(a=t.target.files)?void 0:a[0])||null)},t.oncancel=()=>e(null),t.click()})}async function Z(){try{let e=await H.i7.getPhoto({quality:90,allowEditing:!1,resultType:H.LK.DataUrl,source:H.ru.Photos});if(e.dataUrl){let t=await fetch(e.dataUrl),a=await t.blob();return new File([a],"gallery-image.jpg",{type:"image/jpeg"})}return null}catch(e){return console.error("Error selecting image from gallery:",e),$()}}async function $(){return new Promise(e=>{let t=document.createElement("input");t.type="file",t.accept="image/*",t.onchange=t=>{var a;e((null==(a=t.target.files)?void 0:a[0])||null)},t.oncancel=()=>e(null),t.click()})}let G=e=>{let{cameraMode:t,setCameraMode:a,cameraPhotoFile:c,setCameraPhotoFile:d,cameraPhotoPreview:o,setCameraPhotoPreview:m,cameraRemarks:x,setCameraRemarks:h,isCameraProcessing:u,setIsCameraProcessing:g,processingError:f,setProcessingError:p,setCurrentScreen:b,setSelectedPlant:v}=e,j=(0,r.useCallback)(e=>{h(e.target.value)},[h]),y=async()=>{try{let e=await Q();if(e){d(e);let t=await Y(e);m(t)}}catch(e){console.error("Error capturing image:",e),p("Failed to capture image")}},w=async()=>{try{let e=await Z();if(e){d(e);let t=await Y(e);m(t)}}catch(e){console.error("Error uploading image:",e),p("Failed to upload image")}},N=async()=>{if(c){g(!0),p(null);try{let e=await Y(c);if("identify"===t){let t=await J(e);v({...t,type:"identification",image:e})}else{let t=await K(e,x);v({...t,type:"diagnosis",image:e,remarks:x})}d(null),m(null),h(""),b("results")}catch(e){console.error("Processing error:",e),e instanceof V?p(e.message):p("Failed to process image. Please try again.")}finally{g(!1)}}};return(0,s.jsxs)("div",{className:"min-h-screen bg-black relative",children:[(0,s.jsx)("div",{className:"absolute top-0 left-0 right-0 pt-14 px-6 z-10",children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)(C,{variant:"ghost",size:"sm",onClick:()=>{b("home"),d(null),m(null),h(""),p(null)},className:"text-white hover:bg-white/20 rounded-full px-4",children:"Cancel"})})}),(0,s.jsx)("div",{className:"absolute top-28 left-1/2 transform -translate-x-1/2 z-10",children:(0,s.jsxs)("div",{className:"flex bg-gray-800 rounded-full p-1",children:[(0,s.jsx)(C,{onClick:()=>a("identify"),className:"px-6 py-2 rounded-full font-medium ".concat("identify"===t?"bg-green-600 text-white":"bg-transparent text-gray-400 hover:bg-gray-700"),children:"Identify"}),(0,s.jsx)(C,{onClick:()=>a("diagnose"),className:"px-6 py-2 rounded-full font-medium ".concat("diagnose"===t?"bg-green-600 text-white":"bg-transparent text-gray-400 hover:bg-gray-700"),children:"Diagnose"})]})}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gray-900 flex items-center justify-center",children:o?(0,s.jsxs)("div",{className:"relative w-full h-full",children:[(0,s.jsx)(P.default,{src:o,alt:"Plant Preview",width:400,height:600,className:"w-full h-full object-cover opacity-70"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black/30"})]}):(0,s.jsx)(l.A,{className:"h-24 w-24 text-gray-700"})}),f&&(0,s.jsx)("div",{className:"absolute top-1/2 left-0 right-0 px-6 z-30",children:(0,s.jsxs)("div",{className:"bg-red-600/95 backdrop-blur-sm text-white p-4 rounded-2xl text-center border border-red-500",children:[(0,s.jsx)("p",{className:"font-medium",children:"Error"}),(0,s.jsx)("p",{className:"text-sm mt-1",children:f}),(0,s.jsx)(C,{onClick:()=>p(null),className:"mt-2 bg-red-700 hover:bg-red-800 text-white text-sm px-4 py-1 rounded-full",children:"Dismiss"})]})}),"diagnose"===t&&(0,s.jsx)("div",{className:"absolute bottom-40 left-0 right-0 px-6 z-20",children:(0,s.jsx)("div",{className:"bg-gray-800/95 backdrop-blur-sm rounded-2xl p-4 border border-gray-700",children:(0,s.jsx)(O,{value:x,onChange:j,placeholder:"Enter remarks about your plant's symptoms (e.g., 'Mushy roots', 'Yellowing leaves')",className:"w-full bg-transparent border-none text-white placeholder:text-gray-400 min-h-[80px] resize-none outline-none",rows:3},"diagnose-remarks")})}),(0,s.jsxs)("div",{className:"absolute bottom-8 left-0 right-0 flex justify-center items-center space-x-4 z-10",children:[(0,s.jsx)(C,{onClick:w,className:"w-16 h-16 bg-gray-800 hover:bg-gray-700 rounded-full flex items-center justify-center text-white",children:(0,s.jsx)(i.A,{className:"h-7 w-7"})}),(0,s.jsx)(C,{onClick:N,disabled:!c||u||"diagnose"===t&&!x.trim(),className:"w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-lg transition-all duration-200 active:scale-95 disabled:opacity-50",children:u?(0,s.jsx)(n.A,{className:"h-8 w-8 animate-spin text-gray-700"}):(0,s.jsx)("div",{className:"w-16 h-16 rounded-full bg-white shadow-inner"})}),(0,s.jsx)(C,{onClick:y,className:"w-16 h-16 bg-gray-800 hover:bg-gray-700 rounded-full flex items-center justify-center text-white",children:(0,s.jsx)(l.A,{className:"h-7 w-7"})})]})]})};function ee(){let[e,t]=(0,r.useState)("home"),[a,i]=(0,r.useState)(null),[n,v]=(0,r.useState)("All"),[j,y]=(0,r.useState)([{id:1,name:"Peace Lily",nickname:"Sunny",health:95,lastWateredDate:new Date(Date.now()-1728e5),image:"/placeholder.svg?height=200&width=200",status:"healthy",wateringFrequencyDays:7,notes:"Loves bright indirect light."},{id:2,name:"Rose",nickname:"Rose",health:75,lastWateredDate:new Date(Date.now()-6048e5),image:"/placeholder.svg?height=200&width=200",status:"needs-attention",wateringFrequencyDays:3,notes:"Needs water."},{id:3,name:"Lily",nickname:"Lily",health:88,lastWateredDate:new Date(Date.now()-2592e5),image:"/placeholder.svg?height=200&width=200",status:"healthy",wateringFrequencyDays:5,notes:"Beautiful flowers."}]),[w,N]=(0,r.useState)(j.length+1),[k,z]=(0,r.useState)(!1),S=[{id:1,name:"Fiddle Leaf Fig",date:"Identified on 2024-07-26",image:"/placeholder.svg?height=60&width=60"},{id:2,name:"Succulent",date:"Identified on 2024-07-20",image:"/placeholder.svg?height=60&width=60"}],D=(e,s,r)=>{if(a&&"identification"===a.type){let l={id:w,name:a.name,nickname:e,health:100,lastWateredDate:new Date,image:a.image,status:"healthy",wateringFrequencyDays:s,notes:r};y(e=>[...e,l]),N(e=>e+1),z(!1),t("collection")}},F=()=>(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900 text-white",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 pt-14 pb-6",children:[(0,s.jsx)("h1",{className:"text-xl font-semibold",children:"FloraVision"}),(0,s.jsx)(C,{variant:"ghost",size:"icon",className:"text-white hover:bg-gray-800 rounded-full",children:(0,s.jsx)(c.A,{className:"h-6 w-6"})})]}),(0,s.jsx)("div",{className:"px-6 mb-8",children:(0,s.jsx)("div",{className:"flex space-x-3",children:(0,s.jsx)(C,{onClick:()=>t("camera"),className:"flex-1 bg-green-600 hover:bg-green-700 text-white rounded-full py-3 font-medium",children:"Identify Plant"})})}),(0,s.jsxs)("div",{className:"px-6 mb-8",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"My Plants"}),(0,s.jsx)("div",{className:"grid grid-cols-3 gap-3",children:j.slice(0,3).map(e=>(0,s.jsxs)("div",{className:"bg-gray-800 rounded-2xl p-4 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-700 rounded-xl mx-auto mb-3 overflow-hidden",children:(0,s.jsx)(P.default,{src:e.image||"/placeholder.svg",alt:e.nickname,width:64,height:64,className:"w-full h-full object-cover"})}),(0,s.jsx)("h3",{className:"font-medium text-sm",children:e.nickname}),(0,s.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:["Last watered ",(e=>{let t=new Date;t.setHours(0,0,0,0);let a=new Date(e);a.setHours(0,0,0,0);let s=Math.floor((t.getTime()-a.getTime())/864e5);return 0===s?"Today":1===s?"Yesterday":"".concat(s," days ago")})(e.lastWateredDate)]})]},e.id))})]}),(0,s.jsxs)("div",{className:"px-6 pb-24",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Recent Scans"}),(0,s.jsx)("div",{className:"space-y-3",children:S.map(e=>(0,s.jsxs)("div",{className:"flex items-center space-x-4 bg-gray-800 rounded-2xl p-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-xl overflow-hidden",children:(0,s.jsx)(P.default,{src:e.image||"/placeholder.svg",alt:e.name,width:48,height:48,className:"w-full h-full object-cover"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-medium",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:e.date})]})]},e.id))})]})]}),[_,W]=(0,r.useState)("identify"),[H,V]=(0,r.useState)(null),[Y,B]=(0,r.useState)(null),[J,K]=(0,r.useState)(""),[Q,X]=(0,r.useState)(!1),[Z,$]=(0,r.useState)(null),ee=()=>a?"diagnosis"===a.type?(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900 text-white",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 pt-14 pb-6",children:[(0,s.jsx)(C,{variant:"ghost",size:"icon",onClick:()=>t("home"),className:"text-white hover:bg-gray-800 rounded-full",children:(0,s.jsx)(d.A,{className:"h-6 w-6"})}),(0,s.jsx)("h1",{className:"text-xl font-semibold",children:"Diagnosis Result"}),(0,s.jsx)(C,{variant:"ghost",size:"icon",className:"text-white hover:bg-gray-800 rounded-full",children:(0,s.jsx)(o.A,{className:"h-6 w-6"})})]}),a.image&&(0,s.jsx)("div",{className:"px-6 mb-6",children:(0,s.jsx)("div",{className:"bg-gray-100 rounded-3xl h-64 overflow-hidden",children:(0,s.jsx)(P.default,{src:a.image,alt:"Diagnosed Plant",width:400,height:256,className:"w-full h-full object-cover"})})}),(0,s.jsx)("div",{className:"px-6 mb-8",children:(0,s.jsxs)("div",{className:"bg-gray-800 rounded-3xl p-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-2",children:a.issue}),(0,s.jsxs)("p",{className:"text-gray-400 mb-2",children:["Severity: ",a.severity]}),(0,s.jsxs)("p",{className:"text-green-400 font-medium mb-4",children:["Confidence: ",a.confidence,"%"]}),(0,s.jsx)("p",{className:"text-gray-300",children:a.advice}),a.remarks&&(0,s.jsxs)("p",{className:"text-gray-500 text-sm mt-4",children:["Remarks: ",a.remarks]})]})}),(0,s.jsx)("div",{className:"px-6 pb-24",children:(0,s.jsx)(C,{onClick:()=>t("home"),className:"w-full bg-green-600 hover:bg-green-700 text-white rounded-full py-3 font-medium",children:"Back to Home"})})]}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900 text-white",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 pt-14 pb-6",children:[(0,s.jsx)(C,{variant:"ghost",size:"icon",onClick:()=>t("home"),className:"text-white hover:bg-gray-800 rounded-full",children:(0,s.jsx)(d.A,{className:"h-6 w-6"})}),(0,s.jsx)(C,{variant:"ghost",size:"icon",className:"text-white hover:bg-gray-800 rounded-full",children:(0,s.jsx)(o.A,{className:"h-6 w-6"})})]}),(0,s.jsx)("div",{className:"px-6 mb-6",children:(0,s.jsx)("div",{className:"bg-gray-100 rounded-3xl h-64 overflow-hidden",children:(0,s.jsx)(P.default,{src:a.image,alt:a.name,width:400,height:256,className:"w-full h-full object-cover"})})}),(0,s.jsx)("div",{className:"px-6 mb-8",children:(0,s.jsxs)("div",{className:"bg-gray-800 rounded-3xl p-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-2",children:a.name}),(0,s.jsxs)("p",{className:"text-gray-400 mb-2",children:["Scientific Name: ",a.scientificName]}),a.family&&(0,s.jsxs)("p",{className:"text-gray-400 mb-2",children:["Family: ",a.family]}),(0,s.jsxs)("p",{className:"text-green-400 font-medium",children:["Confidence: ",a.confidence,"%"]}),a.description&&(0,s.jsx)("p",{className:"text-gray-300 mt-4",children:a.description})]})}),a.careInstructions&&(0,s.jsxs)("div",{className:"px-6 mb-8",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Care Requirements"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 bg-gray-800 rounded-2xl p-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-yellow-500 rounded-full flex items-center justify-center",children:(0,s.jsx)(m.A,{className:"h-5 w-5 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Light"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:a.careInstructions.light})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 bg-gray-800 rounded-2xl p-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center",children:(0,s.jsx)(x.A,{className:"h-5 w-5 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Water"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:a.careInstructions.water})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 bg-gray-800 rounded-2xl p-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-red-500 rounded-full flex items-center justify-center",children:(0,s.jsx)(h.A,{className:"h-5 w-5 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Temperature"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:a.careInstructions.temperature})]})]})]})]}),(0,s.jsx)("div",{className:"px-6 pb-24",children:(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)(C,{onClick:()=>z(!0),className:"flex-1 bg-green-600 hover:bg-green-700 text-white rounded-full py-3 font-medium",children:"Add to My Plants"}),(0,s.jsx)(C,{variant:"outline",className:"flex-1 border-gray-600 text-white hover:bg-gray-800 rounded-full py-3 font-medium bg-transparent",children:"Share Result"})]})}),k&&(0,s.jsx)(es,{selectedPlant:a,onClose:()=>z(!1),onSave:D})]}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-900 text-white flex items-center justify-center",children:(0,s.jsx)("p",{children:"No plant data to display."})}),et=()=>(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900 text-white",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 pt-14 pb-6",children:[(0,s.jsx)("h1",{className:"text-xl font-semibold",children:"My Plants"}),(0,s.jsx)(C,{variant:"ghost",size:"icon",className:"text-white hover:bg-gray-800 rounded-full",children:(0,s.jsx)(u.A,{className:"h-6 w-6"})})]}),(0,s.jsx)("div",{className:"px-6 mb-6",children:(0,s.jsx)("div",{className:"flex space-x-6",children:["All","Indoor","Outdoor","Flowering"].map(e=>(0,s.jsx)("button",{onClick:()=>v(e),className:"pb-2 font-medium ".concat(n===e?"text-white border-b-2 border-white":"text-gray-400 hover:text-gray-300"),children:e},e))})}),(0,s.jsx)("div",{className:"px-6 pb-24",children:(0,s.jsx)("div",{className:"grid grid-cols-2 gap-4",children:j.map(e=>(0,s.jsxs)("div",{className:"bg-gray-100 rounded-3xl p-4 text-center",children:[(0,s.jsx)("div",{className:"h-32 bg-gray-200 rounded-2xl mb-4 overflow-hidden",children:(0,s.jsx)(P.default,{src:e.image||"/placeholder.svg",alt:e.nickname,width:150,height:128,className:"w-full h-full object-cover"})}),(0,s.jsx)("h3",{className:"font-semibold text-gray-900",children:e.nickname}),(0,s.jsx)("p",{className:"text-sm mt-1 ".concat("healthy"===e.status?"text-green-600":"text-orange-600"),children:"healthy"===e.status?"Healthy":"Needs Water"})]},e.id))})})]}),ea=()=>(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900 text-white",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 pt-14 pb-8",children:[(0,s.jsx)("h1",{className:"text-xl font-semibold",children:"Profile"}),(0,s.jsx)(C,{variant:"ghost",size:"icon",className:"text-white hover:bg-gray-800 rounded-full",children:(0,s.jsx)(g.A,{className:"h-6 w-6"})})]}),(0,s.jsxs)("div",{className:"px-6 mb-8 text-center",children:[(0,s.jsx)("div",{className:"w-32 h-32 bg-orange-200 rounded-full mx-auto mb-4 overflow-hidden",children:(0,s.jsx)(P.default,{src:"/placeholder.svg?height=128&width=128",alt:"Profile",width:128,height:128,className:"w-full h-full object-cover"})}),(0,s.jsx)("h2",{className:"text-2xl font-bold",children:"Plant Enthusiast"}),(0,s.jsx)("p",{className:"text-gray-400 mt-1",children:"FloraVision User"}),(0,s.jsx)(C,{className:"bg-gray-700 hover:bg-gray-600 text-white rounded-full px-8 py-2 mt-6",children:"Edit Profile"})]}),(0,s.jsxs)("div",{className:"px-6 mb-8",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Settings"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-lg",children:"Notifications"}),(0,s.jsx)(q,{defaultChecked:!0})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between py-4",children:[(0,s.jsx)("span",{className:"text-lg",children:"Terms of Service"}),(0,s.jsx)(f.A,{className:"h-5 w-5 text-gray-400"})]})]})]}),(0,s.jsx)("div",{className:"px-6 pb-24",children:(0,s.jsx)(C,{className:"w-full bg-gray-700 hover:bg-gray-600 text-white rounded-full py-3 font-medium",children:"Log Out"})})]}),es=e=>{let{selectedPlant:t,onClose:a,onSave:l}=e,[i,n]=(0,r.useState)((null==t?void 0:t.name)||""),[c,d]=(0,r.useState)(""),[o,m]=(0,r.useState)("");return(0,s.jsx)(A,{open:!0,onOpenChange:a,children:(0,s.jsxs)(E,{className:"bg-gray-800 border-gray-700 text-white rounded-3xl",children:[(0,s.jsxs)(L,{children:[(0,s.jsxs)(R,{className:"text-xl font-bold",children:["Add ",null==t?void 0:t.name]}),(0,s.jsx)(U,{className:"text-gray-400",children:"Customize care details for your new plant."})]}),(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),l(i,c?Number.parseInt(c,10):void 0,o)},children:[(0,s.jsxs)("div",{className:"space-y-4 py-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(T,{htmlFor:"nickname",className:"text-sm font-medium text-gray-300",children:"Plant Nickname"}),(0,s.jsx)(M,{id:"nickname",value:i,onChange:e=>n(e.target.value),className:"bg-gray-700 border-gray-600 text-white rounded-xl mt-2"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(T,{htmlFor:"watering",className:"text-sm font-medium text-gray-300",children:"Watering Schedule (Days)"}),(0,s.jsx)(M,{id:"watering",type:"number",placeholder:"e.g., 7",value:c,onChange:e=>d(e.target.value),className:"bg-gray-700 border-gray-600 text-white rounded-xl mt-2"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(T,{htmlFor:"notes",className:"text-sm font-medium text-gray-300",children:"Care Notes"}),(0,s.jsx)(O,{id:"notes",placeholder:"Any specific observations or care tips...",value:o,onChange:e=>m(e.target.value),className:"bg-gray-700 border-gray-600 text-white rounded-xl mt-2 min-h-[80px] resize-none"})]})]}),(0,s.jsx)(I,{children:(0,s.jsx)(C,{type:"submit",className:"w-full bg-green-600 hover:bg-green-700 text-white rounded-full py-3 font-medium",children:"Save Plant"})})]})]})})};return(0,s.jsxs)("div",{className:"max-w-sm mx-auto bg-gray-900 min-h-screen relative dark",children:[(()=>{switch(e){case"home":default:return(0,s.jsx)(F,{});case"camera":return(0,s.jsx)(G,{cameraMode:_,setCameraMode:W,cameraPhotoFile:H,setCameraPhotoFile:V,cameraPhotoPreview:Y,setCameraPhotoPreview:B,cameraRemarks:J,setCameraRemarks:K,isCameraProcessing:Q,setIsCameraProcessing:X,processingError:Z,setProcessingError:$,setCurrentScreen:t,setSelectedPlant:i});case"results":return(0,s.jsx)(ee,{});case"collection":return(0,s.jsx)(et,{});case"profile":return(0,s.jsx)(ea,{})}})(),"camera"!==e&&"results"!==e&&(0,s.jsx)("div",{className:"fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-sm bg-gray-900 border-t border-gray-800",children:(0,s.jsxs)("div",{className:"flex items-center justify-around py-3",children:[(0,s.jsxs)(C,{variant:"ghost",size:"sm",onClick:()=>t("home"),className:"flex flex-col items-center py-2 px-4 rounded-lg ".concat("home"===e?"text-white":"text-gray-500"),children:[(0,s.jsx)(p.A,{className:"h-6 w-6 mb-1"}),(0,s.jsx)("span",{className:"text-xs",children:"Home"})]}),(0,s.jsxs)(C,{variant:"ghost",size:"sm",onClick:()=>{t("camera"),W("identify")},className:"flex flex-col items-center py-2 px-4 rounded-lg text-gray-500",children:[(0,s.jsx)(l.A,{className:"h-6 w-6 mb-1"}),(0,s.jsx)("span",{className:"text-xs",children:"Identify Plants"})]}),(0,s.jsxs)(C,{variant:"ghost",size:"sm",onClick:()=>t("collection"),className:"flex flex-col items-center py-2 px-4 rounded-lg ".concat("collection"===e?"text-white":"text-gray-500"),children:[(0,s.jsx)(b.A,{className:"h-6 w-6 mb-1"}),(0,s.jsx)("span",{className:"text-xs",children:"My Plants"})]}),(0,s.jsxs)(C,{variant:"ghost",size:"sm",onClick:()=>t("profile"),className:"flex flex-col items-center py-2 px-4 rounded-lg ".concat("profile"===e?"text-white":"text-gray-500"),children:[(0,s.jsx)(c.A,{className:"h-6 w-6 mb-1"}),(0,s.jsx)("span",{className:"text-xs",children:"Profile"})]})]})})]})}},8098:(e,t,a)=>{Promise.resolve().then(a.bind(a,4884))}},e=>{e.O(0,[385,441,964,358],()=>e(e.s=8098)),_N_E=e.O()}]);